# show_signal_details.py
"""
显示基于TA-Lib的精确交易信号详情
"""

def show_talib_indicators():
    """显示使用的TA-Lib指标"""
    print("🔬 使用的TA-Lib技术指标:")
    print("=" * 60)
    
    indicators = {
        "趋势指标": [
            "SMA (Simple Moving Average) - 简单移动平均",
            "EMA (Exponential Moving Average) - 指数移动平均", 
            "ADX (Average Directional Index) - 平均趋向指数",
            "SAR (Parabolic SAR) - 抛物线转向指标"
        ],
        "动量指标": [
            "RSI (Relative Strength Index) - 相对强弱指数",
            "WILLR (Williams %R) - 威廉指标",
            "CCI (Commodity Channel Index) - 商品通道指数",
            "STOCH (Stochastic) - 随机指标",
            "ULTOSC (Ultimate Oscillator) - 终极振荡器",
            "CMO (Chande Momentum Oscillator) - 钱德动量摆动指标"
        ],
        "MACD指标": [
            "MACD (Moving Average Convergence Divergence) - 指数平滑移动平均线",
            "MACD Signal - MACD信号线",
            "MACD Histogram - MACD柱状图"
        ],
        "成交量指标": [
            "OBV (On Balance Volume) - 平衡成交量",
            "AD (Accumulation/Distribution) - 累积/派发线"
        ],
        "其他指标": [
            "MOM (Momentum) - 动量指标",
            "PPO (Percentage Price Oscillator) - 价格振荡器",
            "TRIX (Triple Exponential Average) - 三重指数平滑平均"
        ]
    }
    
    for category, indicator_list in indicators.items():
        print(f"\n📊 {category}:")
        for indicator in indicator_list:
            print(f"   • {indicator}")

def show_scoring_system():
    """显示评分系统"""
    print("\n\n📈 精确评分系统:")
    print("=" * 60)
    
    scoring = {
        "趋势分析 (30%权重, 最高20分)": [
            "完美多头排列 (价格>SMA20>SMA50>SMA200): +5分",
            "短期多头 (价格>SMA20>SMA50): +3分", 
            "EMA金叉 (EMA12上穿EMA26): +3分",
            "EMA多头 (EMA12>EMA26): +1分",
            "强上升趋势 (ADX>25且DI+>DI-): +2分",
            "SAR支撑 (价格>SAR): +2分",
            "相反信号为负分"
        ],
        
        "动量分析 (25%权重, 最高25分)": [
            "RSI严重超卖 (<20): +4分, 超卖 (<30): +2分",
            "威廉指标超卖 (<-80): +2分",
            "CCI超卖 (<-100): +2分", 
            "随机指标超卖 (<20): +2分",
            "终极振荡器超卖 (<30): +2分",
            "相反信号为负分"
        ],
        
        "MACD分析 (20%权重, 最高16分)": [
            "零轴上方金叉: +4分",
            "零轴下方金叉: +3分",
            "MACD柱状图上升: +1分",
            "零轴上方运行: +1分",
            "相反信号为负分"
        ],
        
        "支撑阻力 (15%权重, 最高12分)": [
            "距离支撑位<1%: +4分, <2%: +2分",
            "突破阻力位: +3分",
            "距离阻力位<1%: -4分, <2%: -2分",
            "跌破支撑位: -3分"
        ],
        
        "成交量分析 (10%权重, 最高6分)": [
            "放量上涨 (成交量>平均1.5倍且涨幅>1%): +2分",
            "OBV量价齐升: +1分",
            "放量下跌: -2分",
            "OBV量价齐跌: -1分"
        ]
    }
    
    for category, rules in scoring.items():
        print(f"\n🎯 {category}:")
        for rule in rules:
            print(f"   • {rule}")

def show_signal_thresholds():
    """显示信号阈值"""
    print("\n\n🎚️ 信号判定阈值:")
    print("=" * 60)
    
    thresholds = [
        ("强烈做多", "≥15分", "多个技术指标强烈看涨，高概率上涨机会"),
        ("做多", "8-14分", "技术指标偏向看涨，温和上涨机会"),
        ("观望", "-7到+7分", "技术指标信号不明确，等待更清晰方向"),
        ("做空", "-8到-14分", "技术指标偏向看跌，温和下跌风险"),
        ("强烈做空", "≤-15分", "多个技术指标强烈看跌，高概率下跌风险")
    ]
    
    for signal, score_range, description in thresholds:
        print(f"\n🎯 {signal} ({score_range}):")
        print(f"   {description}")

def show_advantages():
    """显示TA-Lib方案的优势"""
    print("\n\n✨ TA-Lib精确分析的优势:")
    print("=" * 60)
    
    advantages = [
        "🔬 专业指标: 使用金融行业标准的TA-Lib技术指标",
        "📊 多维分析: 从趋势、动量、MACD、支撑阻力、成交量5个维度综合分析",
        "⚖️ 权重平衡: 科学的权重分配，避免单一指标误导",
        "🎯 精确评分: 量化评分系统，每个信号都有明确的数值依据",
        "📈 动态调整: 实时计算，根据最新市场数据动态调整信号",
        "🔄 信号连续: 5个等级的信号，提供更细致的交易指导",
        "✅ 经过验证: TA-Lib指标经过全球交易者长期验证",
        "🎨 可视化友好: 评分详情便于理解和调试",
        "🔧 参数优化: 可根据不同市场调整各指标权重和阈值"
    ]
    
    for advantage in advantages:
        print(f"   {advantage}")

def main():
    """主函数"""
    print("🎯 基于TA-Lib的精确5种交易信号系统")
    print("=" * 60)
    
    show_talib_indicators()
    show_scoring_system()
    show_signal_thresholds()
    show_advantages()
    
    print("\n\n🚀 使用方法:")
    print("=" * 60)
    print("1. 运行 python test_five_signals.py 测试信号功能")
    print("2. 运行 python main.py 生成实际交易信号")
    print("3. 查看CSV文件中的'交易信号'列和'signal_score'列")
    print("4. 分析图表中的英文交易信号显示")
    
    print("\n💡 注意事项:")
    print("- 信号仅供参考，不构成投资建议")
    print("- 建议结合基本面分析和风险管理")
    print("- 在实际交易前请充分测试和验证")

if __name__ == "__main__":
    main()
