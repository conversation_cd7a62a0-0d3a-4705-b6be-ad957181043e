# config.py

# Binance API配置（即使不交易，获取数据也需要API key）
BINANCE_API_KEY = 'aXUtpXKddc6QEKnowWs4PaaEPahiYeYuMiSxfgFE020GQYutfIXfJc7r5vszJm2T'
BINANCE_API_SECRET = 'UAnWFZecdV2xLwWrzVSmFnqzFPw9aqs7vpmlvP4epkSHSA16BDvNn2YxFNVFUGuH'

# 分析配置
SYMBOL = 'BTCUSDT'  # 交易对
INTERVAL = '1h'     # K线间隔：1m, 5m, 15m, 30m, 1h, 4h, 1d等
DAYS_TO_ANALYZE = 5 # 分析最近5天数据
LOOKBACK_PERIOD = 200  # 获取的K线数量（增加数量以确保有足够数据计算技术指标）
ANALYSIS_WINDOW = 50   # 分析窗口大小（用于计算支撑/压力位）

# 技术指标参数
INDICATOR_PARAMS = {
    'SMA': [20, 50, 200],
    'EMA': [12, 26],
    'RSI': 14,
    'MACD': (12, 26, 9),
    'BBANDS': (20, 2),
    'ATR': 14,
    'OBV': True,
    'ADX': 14,
    'STOCH': (14, 3, 3),
    'FIBONACCI': True  # 斐波那契回撤水平
}

# 支撑/压力位参数
SUPPORT_RESISTANCE_PARAMS = {
    'pivot_points': True,       # 枢轴点
    'recent_high_low': True,    # 近期高/低点
    'volume_profile': True,     # 成交量分布
    'fib_levels': [0, 0.236, 0.382, 0.5, 0.618, 0.786, 1]  # 斐波那契水平
}

# 可视化配置
PLOT_LAST_POINTS = 200  # 图表中显示的最新数据点数