# config.py

# Binance API配置（即使不交易，获取数据也需要API key）
BINANCE_API_KEY = 'aXUtpXKddc6QEKnowWs4PaaEPahiYeYuMiSxfgFE020GQYutfIXfJc7r5vszJm2T'
BINANCE_API_SECRET = 'UAnWFZecdV2xLwWrzVSmFnqzFPw9aqs7vpmlvP4epkSHSA16BDvNn2YxFNVFUGuH'

# 分析配置
SYMBOL = 'BTCUSDT'  # 交易对
INTERVAL = '1h'     # 1小时K线
DAYS_TO_ANALYZE = 7 # 分析最近7天数据
LOOKBACK_PERIOD = 168  # 获取的K线数量（7天 * 24小时）
ANALYSIS_WINDOW = 70   # 分析窗口大小（用于计算支撑/压力位）
PLOT_LAST_POINTS = 168  # 图表显示最后168根K线（7天）

# 技术指标参数
INDICATOR_PARAMS = {
    'SMA': [20, 50, 200],
    'EMA': [12, 26],
    'RSI': 14,
    'MACD': (12, 26, 9),
    'BBANDS': (20, 2),
    'ATR': 14,
    'OBV': True,
    'ADX': 14,
    'STOCH': (14, 3, 3),
    'FIBONACCI': True
}

# 支撑/压力位参数
SUPPORT_RESISTANCE_PARAMS = {
    'pivot_points': True,
    'recent_high_low': True,
    'volume_profile': True,
    'fib_levels': [0, 0.236, 0.382, 0.5, 0.618, 0.786, 1]
}