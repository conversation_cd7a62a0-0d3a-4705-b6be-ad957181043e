# technical_analysis.py
import talib
import pandas as pd
import numpy as np
from config import INDICATOR_PARAMS, SUPPORT_RESISTANCE_PARAMS, DAYS_TO_ANALYZE

def calculate_indicators(df):
    """计算所有技术指标（含支撑/压力位）"""
    # 移动平均线 (SMA)
    for period in INDICATOR_PARAMS['SMA']:
        df[f'SMA_{period}'] = talib.SMA(df['close'], timeperiod=period)
    
    # 指数移动平均线 (EMA)
    for period in INDICATOR_PARAMS['EMA']:
        df[f'EMA_{period}'] = talib.EMA(df['close'], timeperiod=period)
    
    # RSI
    df['RSI'] = talib.RSI(df['close'], timeperiod=INDICATOR_PARAMS['RSI'])
    
    # MACD
    macd, macd_signal, macd_hist = talib.MACD(
        df['close'], 
        fastperiod=INDICATOR_PARAMS['MACD'][0],
        slowperiod=INDICATOR_PARAMS['MACD'][1],
        signalperiod=INDICATOR_PARAMS['MACD'][2]
    )
    df['MACD'] = macd
    df['MACD_signal'] = macd_signal
    df['MACD_hist'] = macd_hist
    
    # 布林带
    upper, middle, lower = talib.BBANDS(
        df['close'], 
        timeperiod=INDICATOR_PARAMS['BBANDS'][0],
        nbdevup=INDICATOR_PARAMS['BBANDS'][1],
        nbdevdn=INDICATOR_PARAMS['BBANDS'][1]
    )
    df['BB_upper'] = upper
    df['BB_middle'] = middle
    df['BB_lower'] = lower
    
    # ATR (平均真实波幅)
    df['ATR'] = talib.ATR(
        df['high'], df['low'], df['close'], 
        timeperiod=INDICATOR_PARAMS['ATR']
    )
    
    # 能量潮 (OBV)
    if INDICATOR_PARAMS['OBV']:
        df['OBV'] = talib.OBV(df['close'], df['volume'])
    
    # ADX (平均趋向指数)
    df['ADX'] = talib.ADX(
        df['high'], df['low'], df['close'], 
        timeperiod=INDICATOR_PARAMS['ADX']
    )
    
    # 随机指标 (Stochastic)
    slowk, slowd = talib.STOCH(
        df['high'], df['low'], df['close'],
        fastk_period=INDICATOR_PARAMS['STOCH'][0],
        slowk_period=INDICATOR_PARAMS['STOCH'][1],
        slowd_period=INDICATOR_PARAMS['STOCH'][2]
    )
    df['STOCH_K'] = slowk
    df['STOCH_D'] = slowd
    
    # 计算支撑位和压力位
    df = calculate_support_resistance(df)
    
    # 添加交易建议
    df = add_trading_suggestions(df)
    
    return df

def calculate_support_resistance(df):
    """计算支撑位和压力位"""
    analysis_window = DAYS_TO_ANALYZE * 24  # 5天 * 24小时
    
    # 1. 近期高点和低点
    df['recent_high'] = df['high'].rolling(window=analysis_window).max()
    df['recent_low'] = df['low'].rolling(window=analysis_window).min()
    
    # 2. 枢轴点系统 (Pivot Points)
    df['pivot'] = (df['high'] + df['low'] + df['close']) / 3
    df['s1'] = (2 * df['pivot']) - df['high']
    df['s2'] = df['pivot'] - (df['high'] - df['low'])
    df['s3'] = df['low'] - 2 * (df['high'] - df['pivot'])
    df['r1'] = (2 * df['pivot']) - df['low']
    df['r2'] = df['pivot'] + (df['high'] - df['low'])
    df['r3'] = df['high'] + 2 * (df['pivot'] - df['low'])
    
    # 3. 斐波那契回撤水平
    if INDICATOR_PARAMS.get('FIBONACCI', False):
        max_price = df['high'].rolling(window=analysis_window).max()
        min_price = df['low'].rolling(window=analysis_window).min()
        diff = max_price - min_price
        
        for level in SUPPORT_RESISTANCE_PARAMS['fib_levels']:
            df[f'fib_{int(level*1000)}'] = max_price - (diff * level)
    
    # 4. 识别关键支撑/压力位
    df = identify_key_levels(df)
    
    return df

def identify_key_levels(df):
    """识别关键支撑/压力位"""
    # 最近的关键水平
    support_cols = ['recent_low', 's1', 's2', 's3']
    resistance_cols = ['recent_high', 'r1', 'r2', 'r3']
    
    # 添加斐波那契水平（如果启用）
    if INDICATOR_PARAMS.get('FIBONACCI', False):
        fib_cols = [f'fib_{int(level*1000)}' for level in SUPPORT_RESISTANCE_PARAMS['fib_levels']]
        support_cols += fib_cols
        resistance_cols += fib_cols
    
    df['key_support'] = df[support_cols].min(axis=1)
    df['key_resistance'] = df[resistance_cols].max(axis=1)
    
    return df

def add_trading_suggestions(df):
    """添加交易建议"""
    # 标记当前价格与关键水平的关系
    df['price_position'] = (df['close'] - df['key_support']) / (df['key_resistance'] - df['key_support'])
    
    # 使用TA-Lib计算精确的交易信号
    def calculate_precise_signals_with_talib(df):
        """使用TA-Lib精确计算5种交易信号"""
        signals = []
        signal_scores = []  # 保存每个点的详细评分

        # 准备价格数据
        high = df['high'].values
        low = df['low'].values
        close = df['close'].values
        volume = df['volume'].values

        # 计算额外的TA-Lib指标
        # 1. 威廉指标
        willr = talib.WILLR(high, low, close, timeperiod=14)

        # 2. CCI商品通道指数
        cci = talib.CCI(high, low, close, timeperiod=14)

        # 3. 动量指标
        mom = talib.MOM(close, timeperiod=10)

        # 4. 价格振荡器
        ppo = talib.PPO(close, fastperiod=12, slowperiod=26, matype=0)

        # 5. 真实强度指数
        trix = talib.TRIX(close, timeperiod=14)

        # 6. 终极振荡器
        ultosc = talib.ULTOSC(high, low, close, timeperiod1=7, timeperiod2=14, timeperiod3=28)

        # 7. 钱德动量摆动指标
        cmo = talib.CMO(close, timeperiod=14)

        # 8. 平衡成交量
        obv = talib.OBV(close, volume)

        # 9. 成交量价格趋势
        ad = talib.AD(high, low, close, volume)

        # 10. 抛物线SAR
        sar = talib.SAR(high, low, acceleration=0.02, maximum=0.2)

        for i in range(len(df)):
            if i < 30:  # 前30个数据点信号不够准确
                signals.append('观望')
                signal_scores.append({'total': 0, 'details': {}})
                continue

            row = df.iloc[i]
            score = 0
            score_details = {}

            # === 1. 趋势分析 (权重: 30%) ===
            trend_score = 0

            # 1.1 移动平均线多空排列 (10分)
            if not pd.isna(row.get('SMA_20')) and not pd.isna(row.get('SMA_50')) and not pd.isna(row.get('SMA_200')):
                if row['close'] > row['SMA_20'] > row['SMA_50'] > row['SMA_200']:
                    trend_score += 5  # 完美多头排列
                elif row['close'] > row['SMA_20'] > row['SMA_50']:
                    trend_score += 3  # 短期多头
                elif row['close'] < row['SMA_20'] < row['SMA_50'] < row['SMA_200']:
                    trend_score -= 5  # 完美空头排列
                elif row['close'] < row['SMA_20'] < row['SMA_50']:
                    trend_score -= 3  # 短期空头

            # 1.2 EMA趋势 (5分)
            if not pd.isna(row.get('EMA_12')) and not pd.isna(row.get('EMA_26')):
                if row['EMA_12'] > row['EMA_26']:
                    if i > 0 and df.iloc[i-1]['EMA_12'] <= df.iloc[i-1]['EMA_26']:
                        trend_score += 3  # EMA金叉
                    else:
                        trend_score += 1  # EMA多头
                else:
                    if i > 0 and df.iloc[i-1]['EMA_12'] >= df.iloc[i-1]['EMA_26']:
                        trend_score -= 3  # EMA死叉
                    else:
                        trend_score -= 1  # EMA空头

            # 1.3 ADX趋势强度确认 (5分)
            if not pd.isna(row.get('ADX')):
                if row['ADX'] > 25:  # 强趋势
                    # 使用DI+和DI-判断方向
                    if not pd.isna(row.get('DI_plus')) and not pd.isna(row.get('DI_minus')):
                        if row['DI_plus'] > row['DI_minus']:
                            trend_score += 2  # 强上升趋势
                        else:
                            trend_score -= 2  # 强下降趋势

            # 1.4 抛物线SAR (5分)
            if not pd.isna(sar[i]):
                if close[i] > sar[i]:
                    trend_score += 2  # SAR支撑
                else:
                    trend_score -= 2  # SAR阻力

            score_details['trend'] = trend_score
            score += trend_score

            # === 2. 动量分析 (权重: 25%) ===
            momentum_score = 0

            # 2.1 RSI超买超卖 (8分)
            if not pd.isna(row['RSI']):
                if row['RSI'] < 20:
                    momentum_score += 4  # 严重超卖
                elif row['RSI'] < 30:
                    momentum_score += 2  # 超卖
                elif row['RSI'] > 80:
                    momentum_score -= 4  # 严重超买
                elif row['RSI'] > 70:
                    momentum_score -= 2  # 超买

            # 2.2 威廉指标 (4分)
            if not pd.isna(willr[i]):
                if willr[i] < -80:
                    momentum_score += 2  # 超卖
                elif willr[i] > -20:
                    momentum_score -= 2  # 超买

            # 2.3 CCI指标 (4分)
            if not pd.isna(cci[i]):
                if cci[i] < -100:
                    momentum_score += 2  # 超卖
                elif cci[i] > 100:
                    momentum_score -= 2  # 超买

            # 2.4 随机指标 (4分)
            if not pd.isna(row.get('STOCH_k')) and not pd.isna(row.get('STOCH_d')):
                if row['STOCH_k'] < 20 and row['STOCH_d'] < 20:
                    momentum_score += 2  # 随机指标超卖
                elif row['STOCH_k'] > 80 and row['STOCH_d'] > 80:
                    momentum_score -= 2  # 随机指标超买

            # 2.5 终极振荡器 (5分)
            if not pd.isna(ultosc[i]):
                if ultosc[i] < 30:
                    momentum_score += 2  # 超卖
                elif ultosc[i] > 70:
                    momentum_score -= 2  # 超买

            score_details['momentum'] = momentum_score
            score += momentum_score

            # === 3. MACD分析 (权重: 20%) ===
            macd_score = 0

            if not pd.isna(row['MACD']) and not pd.isna(row['MACD_signal']) and not pd.isna(row['MACD_hist']):
                # 3.1 MACD金叉死叉 (8分)
                if i > 0:
                    prev_macd = df.iloc[i-1]['MACD']
                    prev_signal = df.iloc[i-1]['MACD_signal']

                    # 金叉
                    if prev_macd <= prev_signal and row['MACD'] > row['MACD_signal']:
                        if row['MACD'] < 0:  # 零轴下方金叉
                            macd_score += 3
                        else:  # 零轴上方金叉
                            macd_score += 4

                    # 死叉
                    elif prev_macd >= prev_signal and row['MACD'] < row['MACD_signal']:
                        if row['MACD'] > 0:  # 零轴上方死叉
                            macd_score -= 3
                        else:  # 零轴下方死叉
                            macd_score -= 4

                # 3.2 MACD柱状图变化 (4分)
                if i > 0:
                    prev_hist = df.iloc[i-1]['MACD_hist']
                    if row['MACD_hist'] > prev_hist and row['MACD_hist'] > 0:
                        macd_score += 1  # 柱状图上升
                    elif row['MACD_hist'] < prev_hist and row['MACD_hist'] < 0:
                        macd_score -= 1  # 柱状图下降

                # 3.3 MACD零轴位置 (4分)
                if row['MACD'] > 0 and row['MACD_signal'] > 0:
                    macd_score += 1  # 零轴上方
                elif row['MACD'] < 0 and row['MACD_signal'] < 0:
                    macd_score -= 1  # 零轴下方

            score_details['macd'] = macd_score
            score += macd_score

            # === 4. 支撑阻力分析 (权重: 15%) ===
            support_resistance_score = 0

            if not pd.isna(row['key_support']) and not pd.isna(row['key_resistance']):
                support_distance = (row['close'] - row['key_support']) / row['key_support'] * 100
                resistance_distance = (row['key_resistance'] - row['close']) / row['close'] * 100

                # 4.1 接近关键位置 (8分)
                if support_distance < 1:  # 距离支撑位1%以内
                    support_resistance_score += 4
                elif support_distance < 2:  # 距离支撑位2%以内
                    support_resistance_score += 2
                elif resistance_distance < 1:  # 距离阻力位1%以内
                    support_resistance_score -= 4
                elif resistance_distance < 2:  # 距离阻力位2%以内
                    support_resistance_score -= 2

                # 4.2 突破确认 (4分)
                if i > 0:
                    prev_close = df.iloc[i-1]['close']
                    # 向上突破阻力位
                    if prev_close <= row['key_resistance'] and row['close'] > row['key_resistance']:
                        support_resistance_score += 3
                    # 向下跌破支撑位
                    elif prev_close >= row['key_support'] and row['close'] < row['key_support']:
                        support_resistance_score -= 3

            score_details['support_resistance'] = support_resistance_score
            score += support_resistance_score

            # === 5. 成交量分析 (权重: 10%) ===
            volume_score = 0

            if i > 20:  # 确保有足够历史数据
                avg_volume = df.iloc[i-20:i]['volume'].mean()
                volume_ratio = row['volume'] / avg_volume
                price_change = row.get('pct_change', 0)

                # 5.1 放量确认 (6分)
                if volume_ratio > 1.5:  # 放量50%以上
                    if price_change > 1:  # 放量上涨
                        volume_score += 2
                    elif price_change < -1:  # 放量下跌
                        volume_score -= 2
                elif volume_ratio < 0.5:  # 缩量50%以上
                    if abs(price_change) > 1:  # 缩量大幅波动（警告信号）
                        volume_score -= 1

                # 5.2 OBV趋势确认 (4分)
                if not pd.isna(obv[i]) and i > 5:
                    obv_trend = (obv[i] - obv[i-5]) / obv[i-5] if obv[i-5] != 0 else 0
                    price_trend = (close[i] - close[i-5]) / close[i-5]

                    # OBV与价格同向
                    if obv_trend > 0.02 and price_trend > 0.02:
                        volume_score += 1  # 量价齐升
                    elif obv_trend < -0.02 and price_trend < -0.02:
                        volume_score -= 1  # 量价齐跌
                    # OBV与价格背离
                    elif obv_trend > 0.02 and price_trend < -0.02:
                        volume_score += 1  # 底背离
                    elif obv_trend < -0.02 and price_trend > 0.02:
                        volume_score -= 1  # 顶背离

            score_details['volume'] = volume_score
            score += volume_score

            # === 根据综合评分确定信号 ===
            total_score = score

            if total_score >= 15:
                signal = '强烈做多'
            elif total_score >= 8:
                signal = '做多'
            elif total_score <= -15:
                signal = '强烈做空'
            elif total_score <= -8:
                signal = '做空'
            else:
                signal = '观望'

            signals.append(signal)
            score_details['total'] = total_score
            signal_scores.append(score_details)

        return signals, signal_scores

    # 计算中文交易信号和详细评分
    signals, signal_scores = calculate_precise_signals_with_talib(df)
    df['trade_suggestion'] = signals
    df['signal_score'] = [score['total'] for score in signal_scores]

    # 创建英文版本的交易信号映射
    signal_mapping = {
        '强烈做多': 'STRONG BUY',
        '做多': 'BUY',
        '观望': 'HOLD',
        '做空': 'SELL',
        '强烈做空': 'STRONG SELL'
    }

    # 添加英文版本的交易信号（用于图表显示）
    df['trade_suggestion_en'] = df['trade_suggestion'].map(signal_mapping)
    
    return df