# technical_analysis.py
import talib
import pandas as pd
import numpy as np
from config import INDICATOR_PARAMS, SUPPORT_RESISTANCE_PARAMS, DAYS_TO_ANALYZE

def calculate_indicators(df):
    """计算所有技术指标（含支撑/压力位）"""
    # 移动平均线 (SMA)
    for period in INDICATOR_PARAMS['SMA']:
        df[f'SMA_{period}'] = talib.SMA(df['close'], timeperiod=period)
    
    # 指数移动平均线 (EMA)
    for period in INDICATOR_PARAMS['EMA']:
        df[f'EMA_{period}'] = talib.EMA(df['close'], timeperiod=period)
    
    # RSI
    df['RSI'] = talib.RSI(df['close'], timeperiod=INDICATOR_PARAMS['RSI'])
    
    # MACD
    macd, macd_signal, macd_hist = talib.MACD(
        df['close'], 
        fastperiod=INDICATOR_PARAMS['MACD'][0],
        slowperiod=INDICATOR_PARAMS['MACD'][1],
        signalperiod=INDICATOR_PARAMS['MACD'][2]
    )
    df['MACD'] = macd
    df['MACD_signal'] = macd_signal
    df['MACD_hist'] = macd_hist
    
    # 布林带
    upper, middle, lower = talib.BBANDS(
        df['close'], 
        timeperiod=INDICATOR_PARAMS['BBANDS'][0],
        nbdevup=INDICATOR_PARAMS['BBANDS'][1],
        nbdevdn=INDICATOR_PARAMS['BBANDS'][1]
    )
    df['BB_upper'] = upper
    df['BB_middle'] = middle
    df['BB_lower'] = lower
    
    # ATR (平均真实波幅)
    df['ATR'] = talib.ATR(
        df['high'], df['low'], df['close'], 
        timeperiod=INDICATOR_PARAMS['ATR']
    )
    
    # 能量潮 (OBV)
    if INDICATOR_PARAMS['OBV']:
        df['OBV'] = talib.OBV(df['close'], df['volume'])
    
    # ADX (平均趋向指数)
    df['ADX'] = talib.ADX(
        df['high'], df['low'], df['close'], 
        timeperiod=INDICATOR_PARAMS['ADX']
    )
    
    # 随机指标 (Stochastic)
    slowk, slowd = talib.STOCH(
        df['high'], df['low'], df['close'],
        fastk_period=INDICATOR_PARAMS['STOCH'][0],
        slowk_period=INDICATOR_PARAMS['STOCH'][1],
        slowd_period=INDICATOR_PARAMS['STOCH'][2]
    )
    df['STOCH_K'] = slowk
    df['STOCH_D'] = slowd
    
    # 计算支撑位和压力位
    df = calculate_support_resistance(df)
    
    # 添加交易建议
    df = add_trading_suggestions(df)
    
    return df

def calculate_support_resistance(df):
    """计算支撑位和压力位"""
    analysis_window = DAYS_TO_ANALYZE * 24  # 5天 * 24小时
    
    # 1. 近期高点和低点
    df['recent_high'] = df['high'].rolling(window=analysis_window).max()
    df['recent_low'] = df['low'].rolling(window=analysis_window).min()
    
    # 2. 枢轴点系统 (Pivot Points)
    df['pivot'] = (df['high'] + df['low'] + df['close']) / 3
    df['s1'] = (2 * df['pivot']) - df['high']
    df['s2'] = df['pivot'] - (df['high'] - df['low'])
    df['s3'] = df['low'] - 2 * (df['high'] - df['pivot'])
    df['r1'] = (2 * df['pivot']) - df['low']
    df['r2'] = df['pivot'] + (df['high'] - df['low'])
    df['r3'] = df['high'] + 2 * (df['pivot'] - df['low'])
    
    # 3. 斐波那契回撤水平
    if INDICATOR_PARAMS.get('FIBONACCI', False):
        max_price = df['high'].rolling(window=analysis_window).max()
        min_price = df['low'].rolling(window=analysis_window).min()
        diff = max_price - min_price
        
        for level in SUPPORT_RESISTANCE_PARAMS['fib_levels']:
            df[f'fib_{int(level*1000)}'] = max_price - (diff * level)
    
    # 4. 识别关键支撑/压力位
    df = identify_key_levels(df)
    
    return df

def identify_key_levels(df):
    """识别关键支撑/压力位"""
    # 最近的关键水平
    support_cols = ['recent_low', 's1', 's2', 's3']
    resistance_cols = ['recent_high', 'r1', 'r2', 'r3']
    
    # 添加斐波那契水平（如果启用）
    if INDICATOR_PARAMS.get('FIBONACCI', False):
        fib_cols = [f'fib_{int(level*1000)}' for level in SUPPORT_RESISTANCE_PARAMS['fib_levels']]
        support_cols += fib_cols
        resistance_cols += fib_cols
    
    df['key_support'] = df[support_cols].min(axis=1)
    df['key_resistance'] = df[resistance_cols].max(axis=1)
    
    return df

def add_trading_suggestions(df):
    """添加交易建议"""
    # 标记当前价格与关键水平的关系
    df['price_position'] = (df['close'] - df['key_support']) / (df['key_resistance'] - df['key_support'])
    
    # 添加交易信号建议 (使用英文避免字体问题)
    df['trade_suggestion'] = np.select(
        [
            df['close'] < df['key_support'] * 1.01,
            df['close'] > df['key_resistance'] * 0.99,
            (df['RSI'] < 35) & (df['MACD'] > df['MACD_signal']),
            (df['RSI'] > 65) & (df['MACD'] < df['MACD_signal'])
        ],
        [
            'STRONG BUY (Near Support)',
            'STRONG SELL (Near Resistance)',
            'BUY Signal (Oversold + MACD Bullish)',
            'SELL Signal (Overbought + MACD Bearish)'
        ],
        default='HOLD (Neutral Zone)'
    )
    
    return df