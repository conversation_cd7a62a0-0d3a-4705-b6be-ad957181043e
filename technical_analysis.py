# technical_analysis.py
import talib
import pandas as pd
import numpy as np
from config import INDICATOR_PARAMS, SUPPORT_RESISTANCE_PARAMS, DAYS_TO_ANALYZE

def calculate_indicators(df):
    """计算所有技术指标（含支撑/压力位）"""
    # 移动平均线 (SMA)
    for period in INDICATOR_PARAMS['SMA']:
        df[f'SMA_{period}'] = talib.SMA(df['close'], timeperiod=period)
    
    # 指数移动平均线 (EMA)
    for period in INDICATOR_PARAMS['EMA']:
        df[f'EMA_{period}'] = talib.EMA(df['close'], timeperiod=period)
    
    # RSI
    df['RSI'] = talib.RSI(df['close'], timeperiod=INDICATOR_PARAMS['RSI'])
    
    # MACD
    macd, macd_signal, macd_hist = talib.MACD(
        df['close'], 
        fastperiod=INDICATOR_PARAMS['MACD'][0],
        slowperiod=INDICATOR_PARAMS['MACD'][1],
        signalperiod=INDICATOR_PARAMS['MACD'][2]
    )
    df['MACD'] = macd
    df['MACD_signal'] = macd_signal
    df['MACD_hist'] = macd_hist
    
    # 布林带
    upper, middle, lower = talib.BBANDS(
        df['close'], 
        timeperiod=INDICATOR_PARAMS['BBANDS'][0],
        nbdevup=INDICATOR_PARAMS['BBANDS'][1],
        nbdevdn=INDICATOR_PARAMS['BBANDS'][1]
    )
    df['BB_upper'] = upper
    df['BB_middle'] = middle
    df['BB_lower'] = lower
    
    # ATR (平均真实波幅)
    df['ATR'] = talib.ATR(
        df['high'], df['low'], df['close'], 
        timeperiod=INDICATOR_PARAMS['ATR']
    )
    
    # 能量潮 (OBV)
    if INDICATOR_PARAMS['OBV']:
        df['OBV'] = talib.OBV(df['close'], df['volume'])
    
    # ADX (平均趋向指数)
    df['ADX'] = talib.ADX(
        df['high'], df['low'], df['close'], 
        timeperiod=INDICATOR_PARAMS['ADX']
    )
    
    # 随机指标 (Stochastic)
    slowk, slowd = talib.STOCH(
        df['high'], df['low'], df['close'],
        fastk_period=INDICATOR_PARAMS['STOCH'][0],
        slowk_period=INDICATOR_PARAMS['STOCH'][1],
        slowd_period=INDICATOR_PARAMS['STOCH'][2]
    )
    df['STOCH_K'] = slowk
    df['STOCH_D'] = slowd
    
    # 计算支撑位和压力位
    df = calculate_support_resistance(df)
    
    # 添加交易建议
    df = add_trading_suggestions(df)
    
    return df

def calculate_support_resistance(df):
    """计算支撑位和压力位"""
    analysis_window = DAYS_TO_ANALYZE * 24  # 5天 * 24小时
    
    # 1. 近期高点和低点
    df['recent_high'] = df['high'].rolling(window=analysis_window).max()
    df['recent_low'] = df['low'].rolling(window=analysis_window).min()
    
    # 2. 枢轴点系统 (Pivot Points)
    df['pivot'] = (df['high'] + df['low'] + df['close']) / 3
    df['s1'] = (2 * df['pivot']) - df['high']
    df['s2'] = df['pivot'] - (df['high'] - df['low'])
    df['s3'] = df['low'] - 2 * (df['high'] - df['pivot'])
    df['r1'] = (2 * df['pivot']) - df['low']
    df['r2'] = df['pivot'] + (df['high'] - df['low'])
    df['r3'] = df['high'] + 2 * (df['pivot'] - df['low'])
    
    # 3. 斐波那契回撤水平
    if INDICATOR_PARAMS.get('FIBONACCI', False):
        max_price = df['high'].rolling(window=analysis_window).max()
        min_price = df['low'].rolling(window=analysis_window).min()
        diff = max_price - min_price
        
        for level in SUPPORT_RESISTANCE_PARAMS['fib_levels']:
            df[f'fib_{int(level*1000)}'] = max_price - (diff * level)
    
    # 4. 识别关键支撑/压力位
    df = identify_key_levels(df)
    
    return df

def identify_key_levels(df):
    """识别关键支撑/压力位"""
    # 最近的关键水平
    support_cols = ['recent_low', 's1', 's2', 's3']
    resistance_cols = ['recent_high', 'r1', 'r2', 'r3']
    
    # 添加斐波那契水平（如果启用）
    if INDICATOR_PARAMS.get('FIBONACCI', False):
        fib_cols = [f'fib_{int(level*1000)}' for level in SUPPORT_RESISTANCE_PARAMS['fib_levels']]
        support_cols += fib_cols
        resistance_cols += fib_cols
    
    df['key_support'] = df[support_cols].min(axis=1)
    df['key_resistance'] = df[resistance_cols].max(axis=1)
    
    return df

def add_trading_suggestions(df):
    """添加交易建议"""
    # 标记当前价格与关键水平的关系
    df['price_position'] = (df['close'] - df['key_support']) / (df['key_resistance'] - df['key_support'])
    
    # 计算综合交易信号（基于多个技术指标）
    def calculate_comprehensive_signals(df):
        """计算综合交易信号"""
        signals = []

        for i in range(len(df)):
            row = df.iloc[i]
            score = 0  # 信号强度评分 (-10到+10)

            # 1. 价格相对于支撑阻力位的位置 (权重: 3)
            if not pd.isna(row['key_support']) and not pd.isna(row['key_resistance']):
                support_distance = (row['close'] - row['key_support']) / row['key_support']
                resistance_distance = (row['key_resistance'] - row['close']) / row['close']

                if support_distance < 0.01:  # 接近支撑位
                    score += 3
                elif support_distance < 0.02:
                    score += 2
                elif resistance_distance < 0.01:  # 接近阻力位
                    score -= 3
                elif resistance_distance < 0.02:
                    score -= 2

            # 2. RSI指标 (权重: 2)
            if not pd.isna(row['RSI']):
                if row['RSI'] < 25:  # 严重超卖
                    score += 2
                elif row['RSI'] < 35:  # 超卖
                    score += 1
                elif row['RSI'] > 75:  # 严重超买
                    score -= 2
                elif row['RSI'] > 65:  # 超买
                    score -= 1

            # 3. MACD指标 (权重: 2)
            if not pd.isna(row['MACD']) and not pd.isna(row['MACD_signal']):
                macd_diff = row['MACD'] - row['MACD_signal']
                if i > 0:
                    prev_macd_diff = df.iloc[i-1]['MACD'] - df.iloc[i-1]['MACD_signal']
                    # 金叉
                    if prev_macd_diff <= 0 and macd_diff > 0:
                        score += 2
                    # 死叉
                    elif prev_macd_diff >= 0 and macd_diff < 0:
                        score -= 2
                    # MACD在零轴上方且上升
                    elif macd_diff > 0 and macd_diff > prev_macd_diff:
                        score += 1
                    # MACD在零轴下方且下降
                    elif macd_diff < 0 and macd_diff < prev_macd_diff:
                        score -= 1

            # 4. 移动平均线趋势 (权重: 2)
            ma_score = 0
            if not pd.isna(row.get('SMA_20')) and not pd.isna(row.get('SMA_50')):
                if row['close'] > row['SMA_20'] > row['SMA_50']:  # 多头排列
                    ma_score += 1
                elif row['close'] < row['SMA_20'] < row['SMA_50']:  # 空头排列
                    ma_score -= 1

                if not pd.isna(row.get('EMA_12')) and not pd.isna(row.get('EMA_26')):
                    if row['EMA_12'] > row['EMA_26']:  # EMA多头
                        ma_score += 1
                    else:  # EMA空头
                        ma_score -= 1
            score += ma_score

            # 5. ADX趋势强度 (权重: 1)
            if not pd.isna(row.get('ADX')):
                if row['ADX'] > 25:  # 强趋势
                    # 根据价格趋势方向调整
                    if i > 5:  # 确保有足够的历史数据
                        recent_trend = (row['close'] - df.iloc[i-5]['close']) / df.iloc[i-5]['close']
                        if recent_trend > 0.02:  # 上升趋势
                            score += 1
                        elif recent_trend < -0.02:  # 下降趋势
                            score -= 1

            # 6. 成交量确认 (权重: 1)
            if i > 10:  # 确保有足够的历史数据
                avg_volume = df.iloc[i-10:i]['volume'].mean()
                if row['volume'] > avg_volume * 1.5:  # 放量
                    # 根据价格变化方向调整
                    price_change = row.get('pct_change', 0)
                    if price_change > 1:  # 放量上涨
                        score += 1
                    elif price_change < -1:  # 放量下跌
                        score -= 1

            # 根据综合评分确定信号
            if score >= 6:
                signal = '强烈做多'
            elif score >= 3:
                signal = '做多'
            elif score <= -6:
                signal = '强烈做空'
            elif score <= -3:
                signal = '做空'
            else:
                signal = '观望'

            signals.append(signal)

        return signals

    # 计算中文交易信号
    df['trade_suggestion'] = calculate_comprehensive_signals(df)

    # 创建英文版本的交易信号映射
    signal_mapping = {
        '强烈做多': 'STRONG BUY',
        '做多': 'BUY',
        '观望': 'HOLD',
        '做空': 'SELL',
        '强烈做空': 'STRONG SELL'
    }

    # 添加英文版本的交易信号（用于图表显示）
    df['trade_suggestion_en'] = df['trade_suggestion'].map(signal_mapping)
    
    return df