# technical_analysis.py
import talib
import pandas as pd
import numpy as np
from config import INDICATOR_PARAMS, SUPPORT_RESISTANCE_PARAMS, DAYS_TO_ANALYZE

# 定义权重和动态阈值
WEIGHTS = {
    'trend': 0.35,  # 趋势指标权重
    'momentum': 0.25,  # 动量指标权重
    'macd': 0.15,  # MACD指标权重
    'support_resistance': 0.15,  # 支撑阻力权重
    'volume': 0.10  # 成交量权重
}


def calculate_indicators(df):
    """计算所有技术指标（含支撑/压力位）"""
    # 移动平均线 (SMA)
    for period in INDICATOR_PARAMS['SMA']:
        df[f'SMA_{period}'] = talib.SMA(df['close'], timeperiod=period)

    # 指数移动平均线 (EMA)
    for period in INDICATOR_PARAMS['EMA']:
        df[f'EMA_{period}'] = talib.EMA(df['close'], timeperiod=period)

    # RSI
    df['RSI'] = talib.RSI(df['close'], timeperiod=INDICATOR_PARAMS['RSI'])

    # MACD
    macd, macd_signal, macd_hist = talib.MACD(
        df['close'],
        fastperiod=INDICATOR_PARAMS['MACD'][0],
        slowperiod=INDICATOR_PARAMS['MACD'][1],
        signalperiod=INDICATOR_PARAMS['MACD'][2]
    )
    df['MACD'] = macd
    df['MACD_signal'] = macd_signal
    df['MACD_hist'] = macd_hist

    # 布林带
    upper, middle, lower = talib.BBANDS(
        df['close'],
        timeperiod=INDICATOR_PARAMS['BBANDS'][0],
        nbdevup=INDICATOR_PARAMS['BBANDS'][1],
        nbdevdn=INDICATOR_PARAMS['BBANDS'][1]
    )
    df['BB_upper'] = upper
    df['BB_middle'] = middle
    df['BB_lower'] = lower

    # ATR (平均真实波幅)
    df['ATR'] = talib.ATR(
        df['high'], df['low'], df['close'],
        timeperiod=INDICATOR_PARAMS['ATR']
    )

    # 能量潮 (OBV)
    if INDICATOR_PARAMS['OBV']:
        df['OBV'] = talib.OBV(df['close'], df['volume'])

    # ADX (平均趋向指数)
    df['ADX'] = talib.ADX(
        df['high'], df['low'], df['close'],
        timeperiod=INDICATOR_PARAMS['ADX']
    )

    # 随机指标 (Stochastic)
    slowk, slowd = talib.STOCH(
        df['high'], df['low'], df['close'],
        fastk_period=INDICATOR_PARAMS['STOCH'][0],
        slowk_period=INDICATOR_PARAMS['STOCH'][1],
        slowd_period=INDICATOR_PARAMS['STOCH'][2]
    )
    df['STOCH_K'] = slowk
    df['STOCH_D'] = slowd

    # === 添加更多精准的TA-Lib指标 ===
    print("  计算扩展技术指标...")

    # 抛物线SAR
    df['SAR'] = talib.SAR(df['high'], df['low'], acceleration=0.02, maximum=0.2)

    # 威廉指标 (Williams %R)
    df['WILLR'] = talib.WILLR(df['high'], df['low'], df['close'], timeperiod=14)

    # 商品通道指数 (CCI)
    df['CCI'] = talib.CCI(df['high'], df['low'], df['close'], timeperiod=14)

    # 动量指标 (MOM)
    df['MOM'] = talib.MOM(df['close'], timeperiod=10)

    # 变化率 (ROC)
    df['ROC'] = talib.ROC(df['close'], timeperiod=10)

    # 终极振荡器 (ULTOSC)
    df['ULTOSC'] = talib.ULTOSC(df['high'], df['low'], df['close'],
                                timeperiod1=7, timeperiod2=14, timeperiod3=28)

    # 钱德动量摆动指标 (CMO)
    df['CMO'] = talib.CMO(df['close'], timeperiod=14)

    # DI+ 和 DI- (方向指标)
    df['DI_plus'] = talib.PLUS_DI(df['high'], df['low'], df['close'], timeperiod=14)
    df['DI_minus'] = talib.MINUS_DI(df['high'], df['low'], df['close'], timeperiod=14)

    # 方向指数 (DX)
    df['DX'] = talib.DX(df['high'], df['low'], df['close'], timeperiod=14)

    # 平均方向指数评级 (ADXR)
    df['ADXR'] = talib.ADXR(df['high'], df['low'], df['close'], timeperiod=14)

    # 累积/派发线 (A/D Line)
    df['AD'] = talib.AD(df['high'], df['low'], df['close'], df['volume'])

    # 累积/派发振荡器 (A/D Oscillator)
    df['ADOSC'] = talib.ADOSC(df['high'], df['low'], df['close'], df['volume'],
                              fastperiod=3, slowperiod=10)

    # 价格振荡器 (PPO)
    df['PPO'] = talib.PPO(df['close'], fastperiod=12, slowperiod=26, matype=0)

    # 三重指数平滑平均 (TRIX)
    df['TRIX'] = talib.TRIX(df['close'], timeperiod=14)

    # 真实波幅 (TRANGE)
    df['TRANGE'] = talib.TRANGE(df['high'], df['low'], df['close'])

    # 标准差
    df['STDDEV'] = talib.STDDEV(df['close'], timeperiod=20, nbdev=1)

    # 方差
    df['VAR'] = talib.VAR(df['close'], timeperiod=20, nbdev=1)

    # 线性回归
    df['LINEARREG'] = talib.LINEARREG(df['close'], timeperiod=14)
    df['LINEARREG_ANGLE'] = talib.LINEARREG_ANGLE(df['close'], timeperiod=14)
    df['LINEARREG_INTERCEPT'] = talib.LINEARREG_INTERCEPT(df['close'], timeperiod=14)
    df['LINEARREG_SLOPE'] = talib.LINEARREG_SLOPE(df['close'], timeperiod=14)

    # 时间序列预测 (TSF)
    df['TSF'] = talib.TSF(df['close'], timeperiod=14)

    # 加权移动平均 (WMA)
    df['WMA_10'] = talib.WMA(df['close'], timeperiod=10)
    df['WMA_20'] = talib.WMA(df['close'], timeperiod=20)

    # 三重指数移动平均 (TEMA)
    df['TEMA_20'] = talib.TEMA(df['close'], timeperiod=20)

    # 三角移动平均 (TRIMA)
    df['TRIMA_20'] = talib.TRIMA(df['close'], timeperiod=20)

    # 卡夫曼自适应移动平均 (KAMA)
    df['KAMA'] = talib.KAMA(df['close'], timeperiod=30)

    # 梅萨自适应移动平均 (MAMA)
    df['MAMA'], df['FAMA'] = talib.MAMA(df['close'], fastlimit=0.5, slowlimit=0.05)

    # 中位数价格
    df['MEDPRICE'] = talib.MEDPRICE(df['high'], df['low'])

    # 典型价格
    df['TYPPRICE'] = talib.TYPPRICE(df['high'], df['low'], df['close'])

    # 加权收盘价
    df['WCLPRICE'] = talib.WCLPRICE(df['high'], df['low'], df['close'])

    # 平均价格
    df['AVGPRICE'] = talib.AVGPRICE(df['open'], df['high'], df['low'], df['close'])

    # 成交量相关指标
    print("  计算成交量指标...")

    # 成交量移动平均
    df['VOL_SMA_10'] = talib.SMA(df['volume'], timeperiod=10)
    df['VOL_SMA_20'] = talib.SMA(df['volume'], timeperiod=20)
    df['VOL_EMA_10'] = talib.EMA(df['volume'], timeperiod=10)

    # 成交量比率
    df['VOL_ratio'] = df['volume'] / df['VOL_SMA_20']

    # 布林带相关计算
    print("  计算布林带扩展指标...")

    # 布林带宽度
    df['BB_width'] = (df['BB_upper'] - df['BB_lower']) / df['BB_middle'] * 100

    # 布林带位置 (%B)
    df['BB_percent'] = (df['close'] - df['BB_lower']) / (df['BB_upper'] - df['BB_lower']) * 100

    # 价格变化相关
    df['pct_change'] = df['close'].pct_change() * 100
    df['price_change'] = df['close'].diff()

    # 希尔伯特变换指标
    print("  计算希尔伯特变换指标...")

    # 希尔伯特变换 - 主导周期
    df['HT_DCPERIOD'] = talib.HT_DCPERIOD(df['close'])

    # 希尔伯特变换 - 主导周期相位
    df['HT_DCPHASE'] = talib.HT_DCPHASE(df['close'])

    # 希尔伯特变换 - 相位器组件
    df['HT_PHASOR_inphase'], df['HT_PHASOR_quadrature'] = talib.HT_PHASOR(df['close'])

    # 希尔伯特变换 - 正弦波
    df['HT_SINE_sine'], df['HT_SINE_leadsine'] = talib.HT_SINE(df['close'])

    # 希尔伯特变换 - 趋势模式
    df['HT_TRENDMODE'] = talib.HT_TRENDMODE(df['close'])

    print("  技术指标计算完成！")

    # 计算支撑位和压力位
    df = calculate_support_resistance(df)

    # 添加交易建议
    df = add_trading_suggestions(df)

    return df


def calculate_support_resistance(df):
    """计算支撑位和压力位"""
    analysis_window = DAYS_TO_ANALYZE * 24  # 5天 * 24小时

    # 1. 近期高点和低点
    df['recent_high'] = df['high'].rolling(window=analysis_window).max()
    df['recent_low'] = df['low'].rolling(window=analysis_window).min()

    # 2. 枢轴点系统 (Pivot Points)
    df['pivot'] = (df['high'] + df['low'] + df['close']) / 3
    df['s1'] = (2 * df['pivot']) - df['high']
    df['s2'] = df['pivot'] - (df['high'] - df['low'])
    df['s3'] = df['low'] - 2 * (df['high'] - df['pivot'])
    df['r1'] = (2 * df['pivot']) - df['low']
    df['r2'] = df['pivot'] + (df['high'] - df['low'])
    df['r3'] = df['high'] + 2 * (df['pivot'] - df['low'])

    # 3. 斐波那契回撤水平
    if INDICATOR_PARAMS.get('FIBONACCI', False):
        max_price = df['high'].rolling(window=analysis_window).max()
        min_price = df['low'].rolling(window=analysis_window).min()
        diff = max_price - min_price

        for level in SUPPORT_RESISTANCE_PARAMS['fib_levels']:
            df[f'fib_{int(level * 1000)}'] = max_price - (diff * level)

    # 4. 识别关键支撑/压力位
    df = identify_key_levels(df)

    return df


def identify_key_levels(df):
    """识别关键支撑/压力位"""
    # 最近的关键水平
    support_cols = ['recent_low', 's1', 's2', 's3']
    resistance_cols = ['recent_high', 'r1', 'r2', 'r3']

    # 添加斐波那契水平（如果启用）
    if INDICATOR_PARAMS.get('FIBONACCI', False):
        fib_cols = [f'fib_{int(level * 1000)}' for level in SUPPORT_RESISTANCE_PARAMS['fib_levels']]
        support_cols += fib_cols
        resistance_cols += fib_cols

    df['key_support'] = df[support_cols].min(axis=1)
    df['key_resistance'] = df[resistance_cols].max(axis=1)

    return df


def add_trading_suggestions(df):
    """添加交易建议 - 优化版"""
    # 标记当前价格与关键水平的关系
    df['price_position'] = (df['close'] - df['key_support']) / (df['key_resistance'] - df['key_support'])

    # 使用TA-Lib计算精确的交易信号
    def calculate_precise_signals_with_talib(df):
        """使用TA-Lib精确计算5种交易信号 - 优化版"""
        signals = []
        signal_scores = []  # 保存每个点的详细评分

        # 准备价格数据
        high = df['high'].values
        low = df['low'].values
        close = df['close'].values
        volume = df['volume'].values

        # 计算额外的TA-Lib指标
        willr = talib.WILLR(high, low, close, timeperiod=14)
        cci = talib.CCI(high, low, close, timeperiod=14)
        ultosc = talib.ULTOSC(high, low, close, timeperiod1=7, timeperiod2=14, timeperiod3=28)
        obv = talib.OBV(close, volume)
        ad = talib.AD(high, low, close, volume)
        sar = talib.SAR(high, low, acceleration=0.02, maximum=0.2)

        # 计算平均ATR用于动态阈值
        atr_mean = df['ATR'].mean() if 'ATR' in df.columns else 1.0

        for i in range(len(df)):
            if i < 30:  # 前30个数据点信号不够准确
                signals.append('观望')
                signal_scores.append({'total': 0, 'details': {}})
                continue

            row = df.iloc[i]
            score = 0
            score_details = {}

            # === 1. 趋势分析 (权重: 35%) ===
            trend_score = 0
            # ... [保持原有趋势分析代码] ...
            score += trend_score * WEIGHTS['trend']
            score_details['trend'] = trend_score

            # === 2. 动量分析 (权重: 25%) ===
            momentum_score = 0
            # ... [保持原有动量分析代码] ...
            score += momentum_score * WEIGHTS['momentum']
            score_details['momentum'] = momentum_score

            # === 3. MACD分析 (权重: 15%) ===
            macd_score = 0
            # ... [保持原有MACD分析代码] ...
            score += macd_score * WEIGHTS['macd']
            score_details['macd'] = macd_score

            # === 4. 支撑阻力分析 (权重: 15%) ===
            support_resistance_score = 0
            # ... [保持原有支撑阻力分析代码] ...
            score += support_resistance_score * WEIGHTS['support_resistance']
            score_details['support_resistance'] = support_resistance_score

            # === 5. 成交量分析 (权重: 10%) ===
            volume_score = 0
            # ... [保持原有成交量分析代码] ...
            score += volume_score * WEIGHTS['volume']
            score_details['volume'] = volume_score

            # === 根据市场波动率动态调整阈值 ===
            atr_ratio = row['ATR'] / atr_mean if not pd.isna(row.get('ATR')) else 1
            volatility_factor = max(0.8, min(1.2, atr_ratio))  # 波动率在0.8-1.2之间调整阈值

            # 动态阈值设置
            strong_buy_threshold = 0.6 * volatility_factor
            buy_threshold = 0.3 * volatility_factor
            strong_sell_threshold = -0.6 * volatility_factor
            sell_threshold = -0.3 * volatility_factor

            # === 根据综合评分确定信号 ===
            if score >= strong_buy_threshold:
                signal = '强烈做多'
            elif score >= buy_threshold:
                signal = '做多'
            elif score <= strong_sell_threshold:
                signal = '强烈做空'
            elif score <= sell_threshold:
                signal = '做空'
            else:
                # 震荡市场特殊处理
                if not pd.isna(row.get('ADX')) and 20 < row['ADX'] < 25 and abs(score) < 0.2:
                    signal = '观望(震荡市)'
                else:
                    signal = '观望'

            # === 指标协同验证 ===
            if signal != '观望' and signal != '观望(震荡市)':
                # 多头信号验证
                if '做多' in signal:
                    if not (row['MACD'] > row['MACD_signal'] and row['RSI'] > 50):
                        signal = '观望'  # 未通过协同验证
                    # 布林带验证
                    if 'BB_percent' in row and row['BB_percent'] > 80:
                        signal = '观望'  # 接近上轨，避免买入

                # 空头信号验证
                elif '做空' in signal:
                    if not (row['MACD'] < row['MACD_signal'] and row['RSI'] < 50):
                        signal = '观望'
                    # 布林带验证
                    if 'BB_percent' in row and row['BB_percent'] < 20:
                        signal = '观望'  # 接近下轨，避免卖出

            signals.append(signal)
            score_details['total'] = score
            signal_scores.append(score_details)

        return signals, signal_scores

    # 计算中文交易信号和详细评分
    signals, signal_scores = calculate_precise_signals_with_talib(df)
    df['trade_suggestion'] = signals
    df['signal_score'] = [score['total'] for score in signal_scores]

    # 创建英文版本的交易信号映射
    signal_mapping = {
        '强烈做多': 'STRONG BUY',
        '做多': 'BUY',
        '观望': 'HOLD',
        '观望(震荡市)': 'HOLD (SIDEWAYS)',
        '做空': 'SELL',
        '强烈做空': 'STRONG SELL'
    }

    # 添加英文版本的交易信号（用于图表显示）
    df['trade_suggestion_en'] = df['trade_suggestion'].map(signal_mapping)

    # 添加趋势质量评估
    df['trend_quality'] = np.where(
        (df['ADX'] > 25) & (df['DI_plus'] > df['DI_minus']),
        '强上升趋势',
        np.where(
            (df['ADX'] > 25) & (df['DI_plus'] < df['DI_minus']),
            '强下降趋势',
            np.where(
                df['ADX'] < 20,
                '无趋势(震荡)',
                '弱趋势'
            )
        )
    )

    # 基于趋势质量调整信号
    def adjust_signal_by_trend(row):
        signal = row['trade_suggestion']
        quality = row['trend_quality']

        if '强烈' in signal and '强' in quality:
            return signal  # 强趋势中的强烈信号保持
        elif '做多' in signal and '上升' in quality:
            return signal
        elif '做空' in signal and '下降' in quality:
            return signal
        else:
            # 趋势与信号不一致时降级
            if '强烈' in signal:
                return signal.replace('强烈', '')
            elif signal in ['做多', '做空']:
                return '观望'
            return signal

    df['trade_suggestion'] = df.apply(adjust_signal_by_trend, axis=1)

    # 过滤低质量信号（波动性不足时）
    if 'ATR' in df.columns:
        atr_quantile = df['ATR'].quantile(0.3)
        df.loc[df['ATR'] < atr_quantile, 'trade_suggestion'] = '观望(低波动)'

    return df