# debug_signals.py
"""
调试交易信号计算问题
"""
import pandas as pd
import numpy as np
from datetime import datetime, timed<PERSON>ta

def debug_signal_calculation():
    """调试信号计算过程"""
    print("🔍 调试交易信号计算...")
    
    try:
        from technical_analysis import calculate_indicators
        from data_fetcher import fetch_klines
        
        # 获取真实数据
        print("获取真实市场数据...")
        df = fetch_klines()
        
        if df is None or len(df) == 0:
            print("❌ 无法获取数据")
            return
        
        print(f"✅ 获取到 {len(df)} 条数据")
        print(f"时间范围: {df.index[0]} 到 {df.index[-1]}")
        
        # 计算技术指标
        print("\n计算技术指标...")
        df = calculate_indicators(df)
        
        # 检查关键指标是否计算成功
        key_indicators = ['RSI', 'MACD', 'MACD_signal', 'SMA_20', 'SMA_50', 'key_support', 'key_resistance']
        
        print("\n关键指标检查:")
        for indicator in key_indicators:
            if indicator in df.columns:
                non_na_count = df[indicator].notna().sum()
                print(f"  {indicator}: {non_na_count}/{len(df)} 个有效值")
                if non_na_count > 0:
                    print(f"    最新值: {df[indicator].iloc[-1]:.4f}")
            else:
                print(f"  {indicator}: ❌ 缺失")
        
        # 检查交易信号
        print(f"\n交易信号分布:")
        if 'trade_suggestion' in df.columns:
            signal_counts = df['trade_suggestion'].value_counts()
            for signal, count in signal_counts.items():
                percentage = (count / len(df)) * 100
                print(f"  {signal}: {count}次 ({percentage:.1f}%)")
        else:
            print("  ❌ 未找到交易信号列")
        
        # 检查信号评分
        if 'signal_score' in df.columns:
            scores = df['signal_score'].dropna()
            if len(scores) > 0:
                print(f"\n信号评分统计:")
                print(f"  最小值: {scores.min():.1f}")
                print(f"  最大值: {scores.max():.1f}")
                print(f"  平均值: {scores.mean():.1f}")
                print(f"  中位数: {scores.median():.1f}")
                
                # 显示最近10个评分
                print(f"\n最近10个信号评分:")
                recent_data = df.tail(10)
                for idx, row in recent_data.iterrows():
                    signal = row.get('trade_suggestion', 'N/A')
                    score = row.get('signal_score', 0)
                    price = row['close']
                    rsi = row.get('RSI', 0)
                    print(f"  {idx.strftime('%m-%d %H:%M')}: {signal:8s} (评分:{score:+4.0f}) "
                          f"价格:{price:7.0f} RSI:{rsi:5.1f}")
            else:
                print("  ❌ 信号评分全部为空")
        else:
            print("  ❌ 未找到信号评分列")
        
        return df
        
    except Exception as e:
        print(f"❌ 调试失败: {e}")
        import traceback
        traceback.print_exc()
        return None

def analyze_signal_conditions():
    """分析信号条件"""
    print("\n\n🔬 分析信号生成条件...")
    
    try:
        df = debug_signal_calculation()
        if df is None:
            return
        
        # 分析最新数据点的详细条件
        latest = df.iloc[-1]
        print(f"\n📊 最新数据点分析 ({df.index[-1]}):")
        print(f"价格: {latest['close']:.2f}")
        
        # 检查各项条件
        conditions = []
        
        # 1. 支撑阻力位条件
        if not pd.isna(latest.get('key_support')) and not pd.isna(latest.get('key_resistance')):
            support = latest['key_support']
            resistance = latest['key_resistance']
            support_dist = (latest['close'] - support) / support * 100
            resistance_dist = (resistance - latest['close']) / latest['close'] * 100
            
            print(f"支撑位: {support:.2f} (距离: {support_dist:+.2f}%)")
            print(f"阻力位: {resistance:.2f} (距离: {resistance_dist:+.2f}%)")
            
            if support_dist < 1:
                conditions.append(f"接近支撑位 (+4分)")
            elif resistance_dist < 1:
                conditions.append(f"接近阻力位 (-4分)")
        else:
            print("❌ 支撑阻力位数据缺失")
        
        # 2. RSI条件
        if not pd.isna(latest.get('RSI')):
            rsi = latest['RSI']
            print(f"RSI: {rsi:.1f}")
            
            if rsi < 20:
                conditions.append(f"RSI严重超卖 (+4分)")
            elif rsi < 30:
                conditions.append(f"RSI超卖 (+2分)")
            elif rsi > 80:
                conditions.append(f"RSI严重超买 (-4分)")
            elif rsi > 70:
                conditions.append(f"RSI超买 (-2分)")
            else:
                conditions.append(f"RSI正常区间 (0分)")
        else:
            print("❌ RSI数据缺失")
        
        # 3. MACD条件
        if not pd.isna(latest.get('MACD')) and not pd.isna(latest.get('MACD_signal')):
            macd = latest['MACD']
            signal = latest['MACD_signal']
            print(f"MACD: {macd:.4f}, 信号线: {signal:.4f}")
            
            if macd > signal:
                conditions.append(f"MACD多头 (+1分)")
            else:
                conditions.append(f"MACD空头 (-1分)")
        else:
            print("❌ MACD数据缺失")
        
        # 4. 移动平均线条件
        sma20 = latest.get('SMA_20')
        sma50 = latest.get('SMA_50')
        if not pd.isna(sma20) and not pd.isna(sma50):
            print(f"SMA20: {sma20:.2f}, SMA50: {sma50:.2f}")
            
            if latest['close'] > sma20 > sma50:
                conditions.append(f"多头排列 (+3分)")
            elif latest['close'] < sma20 < sma50:
                conditions.append(f"空头排列 (-3分)")
            else:
                conditions.append(f"均线混乱 (0分)")
        else:
            print("❌ 移动平均线数据缺失")
        
        print(f"\n满足的条件:")
        for condition in conditions:
            print(f"  • {condition}")
        
        if not conditions:
            print("❌ 没有满足任何信号条件，这就是为什么都是观望！")
        
    except Exception as e:
        print(f"❌ 条件分析失败: {e}")

def check_data_quality():
    """检查数据质量"""
    print("\n\n📋 检查数据质量...")
    
    try:
        from data_fetcher import fetch_klines
        
        df = fetch_klines()
        if df is None:
            print("❌ 无法获取数据")
            return
        
        print(f"数据行数: {len(df)}")
        print(f"数据列数: {len(df.columns)}")
        
        # 检查价格数据
        print(f"\n价格数据检查:")
        print(f"最高价: {df['high'].max():.2f}")
        print(f"最低价: {df['low'].min():.2f}")
        print(f"最新价格: {df['close'].iloc[-1]:.2f}")
        print(f"价格变化范围: {((df['high'].max() - df['low'].min()) / df['close'].iloc[-1] * 100):.2f}%")
        
        # 检查成交量
        print(f"\n成交量检查:")
        print(f"平均成交量: {df['volume'].mean():.0f}")
        print(f"最大成交量: {df['volume'].max():.0f}")
        print(f"最小成交量: {df['volume'].min():.0f}")
        
        # 检查数据完整性
        print(f"\n数据完整性:")
        for col in ['open', 'high', 'low', 'close', 'volume']:
            na_count = df[col].isna().sum()
            print(f"{col}: {na_count} 个缺失值")
        
        return df
        
    except Exception as e:
        print(f"❌ 数据质量检查失败: {e}")
        return None

def main():
    """主调试函数"""
    print("🔍 交易信号调试分析")
    print("=" * 60)
    
    # 1. 检查数据质量
    check_data_quality()
    
    # 2. 调试信号计算
    debug_signal_calculation()
    
    # 3. 分析信号条件
    analyze_signal_conditions()
    
    print("\n" + "=" * 60)
    print("🎯 可能的问题和解决方案:")
    print("=" * 60)
    
    solutions = [
        "1. 数据量不足: 需要至少30个数据点才能计算准确信号",
        "2. 指标计算失败: 检查TA-Lib是否正确安装",
        "3. 阈值设置过高: 当前强烈信号需要≥15分，可能需要调低",
        "4. 市场处于震荡期: 技术指标确实显示应该观望",
        "5. 支撑阻力位计算问题: 检查关键位计算逻辑",
        "6. 时间周期问题: 1小时数据可能信号变化较慢"
    ]
    
    for solution in solutions:
        print(f"   {solution}")
    
    print(f"\n💡 建议:")
    print(f"   • 运行此脚本查看详细调试信息")
    print(f"   • 检查最近市场是否确实处于震荡整理期")
    print(f"   • 考虑调整信号阈值或增加更多数据")

if __name__ == "__main__":
    main()
