# test_signal_fix.py
"""
测试信号修复效果
"""
import pandas as pd
import numpy as np
from datetime import datetime, timedelta

def test_signal_generation():
    """测试信号生成"""
    print("🔧 测试信号修复效果...")
    
    try:
        from technical_analysis import calculate_indicators
        
        # 创建模拟的市场数据
        dates = pd.date_range(start=datetime.now() - timedelta(hours=200), 
                             end=datetime.now(), freq='H')
        
        np.random.seed(42)
        
        # 创建有明显趋势的数据
        base_price = 60000
        
        # 前半段下跌趋势
        trend1 = np.linspace(0, -3000, len(dates)//2)
        # 后半段上涨趋势  
        trend2 = np.linspace(-3000, 2000, len(dates) - len(dates)//2)
        trend = np.concatenate([trend1, trend2])
        
        noise = np.random.randn(len(dates)) * 300
        prices = base_price + trend + noise
        
        # 创建相应的RSI数据
        rsi_values = []
        for i, price in enumerate(prices):
            if i < len(dates)//3:  # 前1/3超卖
                rsi_values.append(25 + np.random.randn() * 5)
            elif i > 2*len(dates)//3:  # 后1/3超买
                rsi_values.append(75 + np.random.randn() * 5)
            else:  # 中间正常
                rsi_values.append(50 + np.random.randn() * 15)
        
        data = {
            'open': prices + np.random.randn(len(dates)) * 50,
            'high': prices + np.abs(np.random.randn(len(dates))) * 100,
            'low': prices - np.abs(np.random.randn(len(dates))) * 100,
            'close': prices,
            'volume': np.random.randint(500, 2000, len(dates)),
            'pct_change': np.random.randn(len(dates)) * 2,
            'TR': np.random.rand(len(dates)) * 100
        }
        
        df = pd.DataFrame(data, index=dates)
        
        # 确保OHLC逻辑正确
        for i in range(len(df)):
            df.iloc[i, df.columns.get_loc('high')] = max(df.iloc[i]['open'], df.iloc[i]['close'], df.iloc[i]['high'])
            df.iloc[i, df.columns.get_loc('low')] = min(df.iloc[i]['open'], df.iloc[i]['close'], df.iloc[i]['low'])
        
        print(f"✅ 创建了 {len(df)} 条测试数据")
        
        # 计算技术指标
        df = calculate_indicators(df)
        
        # 检查信号分布
        if 'trade_suggestion' in df.columns:
            signal_counts = df['trade_suggestion'].value_counts()
            print(f"\n📊 信号分布:")
            total = len(df)
            for signal, count in signal_counts.items():
                percentage = (count / total) * 100
                print(f"  {signal}: {count}次 ({percentage:.1f}%)")
            
            # 检查是否有非观望信号
            non_hold_signals = signal_counts.drop('观望', errors='ignore')
            if len(non_hold_signals) > 0:
                print(f"✅ 成功生成了 {non_hold_signals.sum()} 个非观望信号")
                return True
            else:
                print(f"❌ 仍然只有观望信号")
                return False
        else:
            print(f"❌ 未找到交易信号列")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_real_data():
    """测试真实数据"""
    print(f"\n🌐 测试真实市场数据...")
    
    try:
        from data_fetcher import fetch_klines
        from technical_analysis import calculate_indicators
        
        # 获取真实数据
        df = fetch_klines()
        if df is None or len(df) == 0:
            print(f"❌ 无法获取真实数据")
            return False
        
        print(f"✅ 获取到 {len(df)} 条真实数据")
        
        # 计算指标
        df = calculate_indicators(df)
        
        # 检查信号
        if 'trade_suggestion' in df.columns:
            signal_counts = df['trade_suggestion'].value_counts()
            print(f"\n📊 真实数据信号分布:")
            total = len(df)
            for signal, count in signal_counts.items():
                percentage = (count / total) * 100
                print(f"  {signal}: {count}次 ({percentage:.1f}%)")
            
            # 显示最近的信号
            print(f"\n📈 最近10个信号:")
            recent = df.tail(10)
            for idx, row in recent.iterrows():
                signal = row['trade_suggestion']
                score = row.get('signal_score', 0)
                price = row['close']
                rsi = row.get('RSI', 0)
                print(f"  {idx.strftime('%m-%d %H:%M')}: {signal:8s} (评分:{score:+3.0f}) "
                      f"价格:{price:7.0f} RSI:{rsi:5.1f}")
            
            # 检查是否有改善
            non_hold_count = len(df[df['trade_suggestion'] != '观望'])
            if non_hold_count > 0:
                print(f"✅ 真实数据中有 {non_hold_count} 个非观望信号")
                return True
            else:
                print(f"❌ 真实数据中仍然只有观望信号")
                return False
        else:
            print(f"❌ 未找到交易信号列")
            return False
            
    except Exception as e:
        print(f"❌ 真实数据测试失败: {e}")
        return False

def show_new_thresholds():
    """显示新的阈值设置"""
    print(f"\n🎚️ 新的信号阈值设置:")
    print(f"=" * 50)
    
    thresholds = [
        ("强烈做多", "≥8分", "原来≥15分"),
        ("做多", "4-7分", "原来8-14分"),
        ("观望", "-3到+3分", "原来-7到+7分"),
        ("做空", "-4到-7分", "原来-8到-14分"),
        ("强烈做空", "≤-8分", "原来≤-15分")
    ]
    
    for signal, new_range, old_range in thresholds:
        print(f"  {signal}: {new_range:12s} ({old_range})")
    
    print(f"\n💡 调整原因:")
    print(f"  • 原阈值过于严格，导致很难触发信号")
    print(f"  • 新阈值更符合实际市场波动")
    print(f"  • 保持了5个信号等级的区分度")

def main():
    """主测试函数"""
    print("🔧 交易信号修复测试")
    print("=" * 60)
    
    # 显示新阈值
    show_new_thresholds()
    
    # 测试模拟数据
    print(f"\n" + "=" * 60)
    print("测试结果:")
    print("=" * 60)
    
    sim_ok = test_signal_generation()
    real_ok = test_real_data()
    
    print(f"\n" + "=" * 60)
    print("修复总结:")
    print("=" * 60)
    
    if sim_ok and real_ok:
        print("✅ 信号修复成功！")
        print("✅ 模拟数据和真实数据都能生成多样化信号")
        print("✅ 现在可以正常使用5种交易信号")
    elif sim_ok:
        print("⚠️ 部分修复成功")
        print("✅ 模拟数据能生成多样化信号")
        print("❌ 真实数据可能确实处于震荡期，建议观望")
    else:
        print("❌ 修复可能不完全")
        print("建议进一步调试或检查数据质量")
    
    print(f"\n🚀 下一步:")
    print(f"  • 运行 python main.py 查看修复后的结果")
    print(f"  • 运行 python debug_signals.py 进行详细调试")

if __name__ == "__main__":
    main()
