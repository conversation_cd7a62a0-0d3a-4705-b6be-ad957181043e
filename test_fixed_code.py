# test_fixed_code.py
"""
测试修复后的代码
"""
import sys
import os

def test_imports():
    """测试所有模块导入"""
    print("测试模块导入...")
    
    try:
        from config import (BINANCE_API_KEY, BINANCE_API_SECRET, SYMBOL, 
                           INTERVAL, DAYS_TO_ANALYZE, LOOKBACK_PERIOD, 
                           ANALYSIS_WINDOW, PLOT_LAST_POINTS)
        print("✅ config.py 导入成功")
    except ImportError as e:
        print(f"❌ config.py 导入失败: {e}")
        return False
    
    try:
        from data_fetcher import fetch_klines
        print("✅ data_fetcher.py 导入成功")
    except ImportError as e:
        print(f"❌ data_fetcher.py 导入失败: {e}")
        return False
    
    try:
        from technical_analysis import calculate_indicators
        print("✅ technical_analysis.py 导入成功")
    except ImportError as e:
        print(f"❌ technical_analysis.py 导入失败: {e}")
        return False
    
    try:
        from visualization import (save_main_data_file, generate_analysis_summary, 
                                 generate_technical_chart, generate_analysis_guide)
        print("✅ visualization.py 导入成功")
    except ImportError as e:
        print(f"❌ visualization.py 导入失败: {e}")
        return False
    
    return True

def test_config_values():
    """测试配置值"""
    print("\n测试配置值...")
    
    from config import SYMBOL, INTERVAL, DAYS_TO_ANALYZE, PLOT_LAST_POINTS
    
    print(f"交易对: {SYMBOL}")
    print(f"时间间隔: {INTERVAL}")
    print(f"分析天数: {DAYS_TO_ANALYZE}")
    print(f"图表显示点数: {PLOT_LAST_POINTS}")
    
    # 验证配置合理性
    if DAYS_TO_ANALYZE <= 0:
        print("❌ DAYS_TO_ANALYZE 应该大于0")
        return False
    
    if PLOT_LAST_POINTS <= 0:
        print("❌ PLOT_LAST_POINTS 应该大于0")
        return False
    
    print("✅ 配置值验证通过")
    return True

def test_data_fetcher():
    """测试数据获取功能"""
    print("\n测试数据获取...")
    
    try:
        from data_fetcher import fetch_klines
        from binance import Client
        from config import BINANCE_API_KEY, BINANCE_API_SECRET
        
        # 测试API连接
        client = Client(BINANCE_API_KEY, BINANCE_API_SECRET)
        server_time = client.get_server_time()
        print(f"✅ Binance API连接正常，服务器时间: {server_time}")
        
        # 测试数据获取（只获取少量数据进行测试）
        print("测试获取少量K线数据...")
        klines = client.get_klines(symbol='BTCUSDT', interval='1h', limit=5)
        if len(klines) > 0:
            print(f"✅ 成功获取 {len(klines)} 条测试数据")
            return True
        else:
            print("❌ 未获取到数据")
            return False
            
    except Exception as e:
        print(f"❌ 数据获取测试失败: {e}")
        return False

def test_directory_structure():
    """测试目录结构"""
    print("\n测试目录结构...")
    
    required_files = [
        'config.py',
        'data_fetcher.py', 
        'technical_analysis.py',
        'visualization.py',
        'main.py'
    ]
    
    missing_files = []
    for file in required_files:
        if not os.path.exists(file):
            missing_files.append(file)
    
    if missing_files:
        print(f"❌ 缺少文件: {missing_files}")
        return False
    else:
        print("✅ 所有必需文件都存在")
        return True

def main():
    """运行所有测试"""
    print("=" * 50)
    print("代码修复验证测试")
    print("=" * 50)
    
    tests = [
        ("目录结构", test_directory_structure),
        ("模块导入", test_imports),
        ("配置值", test_config_values),
        ("数据获取", test_data_fetcher)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name} 测试通过")
            else:
                print(f"❌ {test_name} 测试失败")
        except Exception as e:
            print(f"❌ {test_name} 测试异常: {e}")
    
    print("\n" + "=" * 50)
    print(f"测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！代码修复成功，可以运行 main.py")
    else:
        print("⚠️  部分测试失败，请检查相关问题")
    
    print("=" * 50)

if __name__ == "__main__":
    main()
