# test_chinese_weekday.py
"""
测试中文星期显示功能
"""
import pandas as pd
import numpy as np
from datetime import datetime, timedelta

def test_chinese_weekday():
    """测试中文星期显示"""
    print("测试中文星期显示功能...")
    
    # 创建一周的测试数据
    start_date = datetime(2025, 7, 14)  # 2025年7月14日是星期一
    dates = pd.date_range(start=start_date, periods=7, freq='D')
    
    # 测试星期映射
    weekday_mapping = {
        'Monday': '星期一', 'Tuesday': '星期二', 'Wednesday': '星期三',
        'Thursday': '星期四', 'Friday': '星期五', 'Saturday': '星期六', 'Sunday': '星期日'
    }
    
    print("星期映射测试:")
    for date in dates:
        english_day = date.strftime('%A')
        chinese_day = weekday_mapping[english_day]
        print(f"  {date.strftime('%Y-%m-%d')} ({english_day}) -> {chinese_day}")
    
    return True

def test_csv_with_chinese_weekday():
    """测试CSV文件中的中文星期"""
    print("\n测试CSV文件中的中文星期...")
    
    try:
        from technical_analysis import calculate_indicators
        from report_generator import save_optimized_csv
        
        # 创建一周的测试数据
        start_date = datetime(2025, 7, 14)  # 星期一
        dates = pd.date_range(start=start_date, periods=7*24, freq='H')  # 一周的小时数据
        
        np.random.seed(42)
        data = {
            'open': 60000 + np.random.randn(len(dates)) * 100,
            'high': 60000 + np.random.randn(len(dates)) * 100 + 50,
            'low': 60000 + np.random.randn(len(dates)) * 100 - 50,
            'close': 60000 + np.random.randn(len(dates)) * 100,
            'volume': np.random.randint(100, 1000, len(dates)),
            'pct_change': np.random.randn(len(dates)) * 2,
            'TR': np.random.rand(len(dates)) * 100
        }
        
        df = pd.DataFrame(data, index=dates)
        df = calculate_indicators(df)
        
        # 生成CSV文件
        csv_file = save_optimized_csv(df)
        
        # 读取CSV文件
        df_csv = pd.read_csv(csv_file, encoding='utf-8-sig')
        
        # 检查星期列
        if '星期' in df_csv.columns:
            unique_weekdays = df_csv['星期'].unique()
            print(f"CSV中的星期值: {list(unique_weekdays)}")
            
            # 检查是否为中文
            chinese_weekdays = ['星期一', '星期二', '星期三', '星期四', '星期五', '星期六', '星期日']
            is_chinese = all(day in chinese_weekdays for day in unique_weekdays)
            
            if is_chinese:
                print("✅ 星期显示为中文")
                
                # 显示星期分布
                weekday_counts = df_csv['星期'].value_counts()
                print("星期分布:")
                for day, count in weekday_counts.items():
                    print(f"  {day}: {count}条记录")
                
                return True
            else:
                print("❌ 星期显示不是中文")
                return False
        else:
            print("❌ 未找到星期列")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def show_csv_sample():
    """显示CSV文件样例"""
    print("\n生成CSV文件样例...")
    
    try:
        from technical_analysis import calculate_indicators
        from report_generator import save_optimized_csv
        
        # 创建简单测试数据
        dates = pd.date_range(start=datetime(2025, 7, 14), periods=24, freq='H')
        
        np.random.seed(42)
        data = {
            'open': 60000 + np.random.randn(len(dates)) * 50,
            'high': 60000 + np.random.randn(len(dates)) * 50 + 25,
            'low': 60000 + np.random.randn(len(dates)) * 50 - 25,
            'close': 60000 + np.random.randn(len(dates)) * 50,
            'volume': np.random.randint(100, 500, len(dates)),
            'pct_change': np.random.randn(len(dates)) * 1,
            'TR': np.random.rand(len(dates)) * 50
        }
        
        df = pd.DataFrame(data, index=dates)
        df = calculate_indicators(df)
        
        # 生成CSV文件
        csv_file = save_optimized_csv(df)
        
        # 读取并显示前几行
        df_csv = pd.read_csv(csv_file, encoding='utf-8-sig')
        
        print("CSV文件前5行预览:")
        # 只显示时间相关列
        time_cols = ['月日', '时分', '小时', '星期']
        available_time_cols = [col for col in time_cols if col in df_csv.columns]
        
        if available_time_cols:
            print(df_csv[available_time_cols].head().to_string(index=False))
        
        return True
        
    except Exception as e:
        print(f"❌ 样例生成失败: {e}")
        return False

def main():
    """主测试函数"""
    print("=" * 60)
    print("中文星期显示测试")
    print("=" * 60)
    
    # 1. 测试星期映射
    mapping_ok = test_chinese_weekday()
    
    # 2. 测试CSV文件
    csv_ok = test_csv_with_chinese_weekday()
    
    # 3. 显示样例
    sample_ok = show_csv_sample()
    
    print("\n" + "=" * 60)
    print("测试总结:")
    print("=" * 60)
    
    if mapping_ok and csv_ok and sample_ok:
        print("✅ 所有测试通过！")
        print("✅ 星期数据已成功改为中文显示")
    else:
        print("❌ 部分测试失败")
    
    print("\n💡 修改效果:")
    print("- 星期列数据: Monday -> 星期一")
    print("- 星期列数据: Tuesday -> 星期二")
    print("- 星期列数据: Wednesday -> 星期三")
    print("- 等等...")
    print("\n现在CSV文件中的星期列完全使用中文显示！")

if __name__ == "__main__":
    main()
