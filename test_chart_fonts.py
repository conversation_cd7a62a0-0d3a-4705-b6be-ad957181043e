# test_chart_fonts.py
"""
测试图表字体显示问题
"""
import matplotlib.pyplot as plt
import matplotlib.font_manager as fm
import numpy as np
from datetime import datetime, timedelta

def list_available_fonts():
    """列出可用字体"""
    print("可用字体列表:")
    fonts = [f.name for f in fm.fontManager.ttflist]
    fonts = sorted(list(set(fonts)))
    
    for i, font in enumerate(fonts[:20]):  # 只显示前20个
        print(f"{i+1:2d}. {font}")
    
    if len(fonts) > 20:
        print(f"... 还有 {len(fonts)-20} 个字体")
    
    return fonts

def test_font_rendering():
    """测试字体渲染"""
    print("\n测试字体渲染...")
    
    # 设置字体
    plt.rcParams['font.family'] = ['DejaVu Sans', 'Arial', 'sans-serif']
    plt.rcParams['font.size'] = 12
    
    # 创建测试图表
    fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(12, 8))
    
    # 生成测试数据
    dates = [datetime.now() - timedelta(hours=i) for i in range(24, 0, -1)]
    prices = 60000 + np.random.randn(24) * 1000
    rsi = 50 + np.random.randn(24) * 20
    
    # 价格图表
    ax1.plot(dates, prices, label='BTC Price', color='black', linewidth=2)
    ax1.axhline(y=prices[-1], color='red', linestyle='--', label=f'Current: ${prices[-1]:.0f}')
    ax1.set_title('BTCUSDT Price Chart - Font Test', fontsize=14, fontweight='bold')
    ax1.set_ylabel('Price (USDT)', fontsize=12)
    ax1.legend(fontsize=10)
    ax1.grid(True, alpha=0.3)
    
    # 添加价格标注
    ax1.text(dates[-1], prices[-1], f'${prices[-1]:.0f}', 
             fontsize=10, bbox=dict(boxstyle="round,pad=0.3", facecolor="yellow", alpha=0.7))
    
    # RSI图表
    ax2.plot(dates, rsi, label='RSI', color='purple', linewidth=2)
    ax2.axhline(70, color='red', linestyle='--', label='Overbought (70)', alpha=0.8)
    ax2.axhline(30, color='green', linestyle='--', label='Oversold (30)', alpha=0.8)
    ax2.fill_between(dates, 30, 70, color='yellow', alpha=0.1, label='Normal Range')
    ax2.set_title('RSI Indicator', fontsize=12)
    ax2.set_ylabel('RSI', fontsize=12)
    ax2.set_ylim(0, 100)
    ax2.legend(fontsize=10)
    ax2.grid(True, alpha=0.3)
    
    # 添加RSI值标注
    ax2.text(dates[-1], rsi[-1], f'RSI: {rsi[-1]:.1f}', 
             fontsize=10, bbox=dict(boxstyle="round,pad=0.3", facecolor="lightblue", alpha=0.7))
    
    # 总标题
    plt.suptitle(f'Font Test Chart - {datetime.now().strftime("%Y-%m-%d %H:%M")}', 
                fontsize=16, fontweight='bold')
    
    plt.tight_layout()
    plt.subplots_adjust(top=0.93)
    
    # 保存测试图片
    test_filename = 'font_test_chart.png'
    plt.savefig(test_filename, dpi=150, bbox_inches='tight', facecolor='white', 
                edgecolor='none', format='png')
    
    print(f"✅ 测试图表已保存: {test_filename}")
    print("请检查图片中的文字是否清晰可见")
    
    plt.show()
    plt.close()

def test_simple_chart():
    """测试简单图表"""
    print("\n创建简单测试图表...")
    
    # 使用最基本的设置
    plt.figure(figsize=(10, 6))
    
    # 简单数据
    x = np.arange(10)
    y = np.random.randn(10) * 10 + 100
    
    plt.plot(x, y, 'b-', linewidth=2, label='Test Data')
    plt.title('Simple Test Chart', fontsize=16)
    plt.xlabel('Time', fontsize=12)
    plt.ylabel('Value', fontsize=12)
    plt.legend(fontsize=10)
    plt.grid(True)
    
    # 添加文字标注
    plt.text(5, y[5], f'Value: {y[5]:.1f}', 
             fontsize=12, bbox=dict(boxstyle="round", facecolor="white", alpha=0.8))
    
    simple_filename = 'simple_test_chart.png'
    plt.savefig(simple_filename, dpi=150, bbox_inches='tight')
    print(f"✅ 简单图表已保存: {simple_filename}")
    
    plt.show()
    plt.close()

def check_matplotlib_backend():
    """检查matplotlib后端"""
    print(f"\nMatplotlib 后端: {plt.get_backend()}")
    print(f"Matplotlib 版本: {plt.__version__}")
    
    # 尝试设置不同的后端
    try:
        import matplotlib
        print(f"可用后端: {matplotlib.backend_bases.Backend}")
    except:
        print("无法获取后端信息")

def main():
    """主测试函数"""
    print("=" * 60)
    print("图表字体显示测试")
    print("=" * 60)
    
    # 1. 检查matplotlib信息
    check_matplotlib_backend()
    
    # 2. 列出可用字体
    fonts = list_available_fonts()
    
    # 3. 测试简单图表
    test_simple_chart()
    
    # 4. 测试复杂图表
    test_font_rendering()
    
    print("\n" + "=" * 60)
    print("测试完成！")
    print("请检查生成的图片文件:")
    print("1. simple_test_chart.png - 简单测试图表")
    print("2. font_test_chart.png - 字体测试图表")
    print("\n如果图片中文字不清晰，可能的解决方案:")
    print("1. 更新matplotlib: pip install --upgrade matplotlib")
    print("2. 安装字体包: pip install matplotlib[fonts]")
    print("3. 检查系统字体设置")
    print("=" * 60)

if __name__ == "__main__":
    main()
