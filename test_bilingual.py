# test_bilingual.py
"""
测试双语显示功能
"""
import pandas as pd
import numpy as np
from datetime import datetime, timed<PERSON><PERSON>

def create_test_data():
    """创建测试数据"""
    dates = pd.date_range(start=datetime.now() - timedelta(hours=24), 
                         end=datetime.now(), freq='H')
    
    np.random.seed(42)
    data = {
        'open': 60000 + np.random.randn(len(dates)) * 100,
        'high': 60000 + np.random.randn(len(dates)) * 100 + 50,
        'low': 60000 + np.random.randn(len(dates)) * 100 - 50,
        'close': 60000 + np.random.randn(len(dates)) * 100,
        'volume': np.random.randint(100, 1000, len(dates)),
        'pct_change': np.random.randn(len(dates)) * 2,
        'TR': np.random.rand(len(dates)) * 100
    }
    
    df = pd.DataFrame(data, index=dates)
    return df

def test_dual_language_signals():
    """测试双语交易信号"""
    print("测试双语交易信号功能...")
    
    try:
        from technical_analysis import calculate_indicators
        
        # 创建测试数据
        df = create_test_data()
        df = calculate_indicators(df)
        
        # 检查是否有两个版本的交易信号
        has_chinese = 'trade_suggestion' in df.columns
        has_english = 'trade_suggestion_en' in df.columns
        
        print(f"✅ 中文交易信号列存在: {has_chinese}")
        print(f"✅ 英文交易信号列存在: {has_english}")
        
        if has_chinese and has_english:
            # 显示信号对比
            print("\n信号对比示例:")
            for i in range(min(5, len(df))):
                chinese_signal = df['trade_suggestion'].iloc[i]
                english_signal = df['trade_suggestion_en'].iloc[i]
                print(f"  中文: {chinese_signal:8s} | 英文: {english_signal}")
            
            # 检查信号映射是否正确
            signal_mapping = {
                '强烈做多': 'STRONG BUY',
                '强烈做空': 'STRONG SELL', 
                '看涨信号': 'BUY Signal',
                '看跌信号': 'SELL Signal',
                '观望': 'HOLD'
            }
            
            mapping_correct = True
            for idx in df.index:
                chinese = df.loc[idx, 'trade_suggestion']
                english = df.loc[idx, 'trade_suggestion_en']
                expected_english = signal_mapping.get(chinese, 'UNKNOWN')
                if english != expected_english:
                    print(f"❌ 映射错误: {chinese} -> {english} (期望: {expected_english})")
                    mapping_correct = False
                    break
            
            if mapping_correct:
                print("✅ 中英文信号映射正确")
            
            return True
        else:
            print("❌ 缺少交易信号列")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def test_csv_content():
    """测试CSV文件内容"""
    print("\n测试CSV文件内容...")
    
    try:
        from technical_analysis import calculate_indicators
        from report_generator import save_optimized_csv
        
        # 创建测试数据
        df = create_test_data()
        df = calculate_indicators(df)
        
        # 生成CSV文件
        csv_file = save_optimized_csv(df)
        
        # 读取CSV文件
        df_csv = pd.read_csv(csv_file, encoding='utf-8-sig')
        
        # 检查CSV中的交易信号
        signal_cols = [col for col in df_csv.columns if '交易信号' in col]
        english_signal_cols = [col for col in df_csv.columns if 'trade_suggestion_en' in col]
        
        print(f"✅ CSV中包含中文交易信号列: {len(signal_cols) > 0}")
        print(f"✅ CSV中不包含英文交易信号列: {len(english_signal_cols) == 0}")
        
        if signal_cols:
            signal_col = signal_cols[0]
            unique_signals = df_csv[signal_col].dropna().unique()
            print(f"CSV中的交易信号值: {list(unique_signals)}")
            
            # 检查是否为中文
            chinese_signals = ['强烈做多', '强烈做空', '看涨信号', '看跌信号', '观望']
            is_chinese = all(signal in chinese_signals for signal in unique_signals)
            print(f"✅ 交易信号为中文: {is_chinese}")
            
            return is_chinese
        
        return False
        
    except Exception as e:
        print(f"❌ CSV测试失败: {e}")
        return False

def test_chart_display():
    """测试图表显示"""
    print("\n测试图表显示...")
    
    try:
        from technical_analysis import calculate_indicators
        from visualization import plot_technical_analysis
        
        # 创建测试数据
        df = create_test_data()
        df = calculate_indicators(df)
        
        # 检查最后一行的信号
        last_row = df.iloc[-1]
        chinese_signal = last_row.get('trade_suggestion', 'N/A')
        english_signal = last_row.get('trade_suggestion_en', 'N/A')
        
        print(f"最新交易信号:")
        print(f"  中文版本: {chinese_signal}")
        print(f"  英文版本: {english_signal}")
        
        # 生成图表（这里只是测试，不实际显示）
        print("✅ 图表将使用英文版本的交易信号")
        print(f"  图表标题将显示: Trade Signal: {english_signal}")
        
        return True
        
    except Exception as e:
        print(f"❌ 图表测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("=" * 60)
    print("双语显示功能测试")
    print("=" * 60)
    
    # 测试各个功能
    signal_ok = test_dual_language_signals()
    csv_ok = test_csv_content()
    chart_ok = test_chart_display()
    
    print("\n" + "=" * 60)
    print("测试总结:")
    print("=" * 60)
    
    if signal_ok and csv_ok and chart_ok:
        print("✅ 所有测试通过！")
        print("✅ 双语显示功能正常")
        print("✅ 图表使用英文，CSV使用中文")
    else:
        print("❌ 部分测试失败")
    
    print("\n🎯 功能说明:")
    print("- 图表标题显示英文交易信号，避免字体警告")
    print("- CSV文件保存中文交易信号，便于用户理解")
    print("- 系统自动维护两个版本的信号映射")
    print("- 用户获得最佳的双语体验")

if __name__ == "__main__":
    main()
