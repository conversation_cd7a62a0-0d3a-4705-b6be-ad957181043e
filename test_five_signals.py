# test_five_signals.py
"""
测试5种交易信号功能
"""
import pandas as pd
import numpy as np
from datetime import datetime, timedelta

def create_test_data():
    """创建测试数据"""
    dates = pd.date_range(start=datetime.now() - timedelta(hours=200), 
                         end=datetime.now(), freq='H')
    
    np.random.seed(42)
    
    # 创建有趋势的价格数据
    base_price = 60000
    trend = np.linspace(0, 2000, len(dates))  # 上升趋势
    noise = np.random.randn(len(dates)) * 200
    prices = base_price + trend + noise
    
    data = {
        'open': prices + np.random.randn(len(dates)) * 50,
        'high': prices + np.abs(np.random.randn(len(dates))) * 100,
        'low': prices - np.abs(np.random.randn(len(dates))) * 100,
        'close': prices,
        'volume': np.random.randint(500, 2000, len(dates)),
        'pct_change': np.random.randn(len(dates)) * 2,
        'TR': np.random.rand(len(dates)) * 100
    }
    
    df = pd.DataFrame(data, index=dates)
    
    # 确保OHLC逻辑正确
    for i in range(len(df)):
        df.iloc[i, df.columns.get_loc('high')] = max(df.iloc[i]['open'], df.iloc[i]['close'], df.iloc[i]['high'])
        df.iloc[i, df.columns.get_loc('low')] = min(df.iloc[i]['open'], df.iloc[i]['close'], df.iloc[i]['low'])
    
    return df

def test_signal_distribution():
    """测试交易信号分布"""
    print("测试5种交易信号分布...")
    
    try:
        from technical_analysis import calculate_indicators
        
        # 创建测试数据
        df = create_test_data()
        df = calculate_indicators(df)
        
        # 统计信号分布
        signal_counts = df['trade_suggestion'].value_counts()
        signal_en_counts = df['trade_suggestion_en'].value_counts()
        
        print("中文交易信号分布:")
        for signal, count in signal_counts.items():
            percentage = (count / len(df)) * 100
            print(f"  {signal}: {count}次 ({percentage:.1f}%)")
        
        print("\n英文交易信号分布:")
        for signal, count in signal_en_counts.items():
            percentage = (count / len(df)) * 100
            print(f"  {signal}: {count}次 ({percentage:.1f}%)")
        
        # 检查是否有5种信号
        expected_signals = ['强烈做多', '做多', '观望', '做空', '强烈做空']
        actual_signals = set(signal_counts.keys())
        
        print(f"\n期望的5种信号: {expected_signals}")
        print(f"实际出现的信号: {list(actual_signals)}")
        
        has_all_signals = all(signal in actual_signals for signal in expected_signals)
        print(f"✅ 包含所有5种信号: {has_all_signals}")
        
        return has_all_signals
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_signal_logic():
    """测试信号逻辑的合理性"""
    print("\n测试信号逻辑合理性...")
    
    try:
        from technical_analysis import calculate_indicators
        
        # 创建测试数据
        df = create_test_data()
        df = calculate_indicators(df)
        
        # 分析信号转换
        signal_changes = []
        for i in range(1, len(df)):
            prev_signal = df.iloc[i-1]['trade_suggestion']
            curr_signal = df.iloc[i]['trade_suggestion']
            if prev_signal != curr_signal:
                signal_changes.append((i, prev_signal, curr_signal))
        
        print(f"信号变化次数: {len(signal_changes)}")
        
        # 显示前几次信号变化
        print("前10次信号变化:")
        for i, (idx, prev, curr) in enumerate(signal_changes[:10]):
            timestamp = df.index[idx]
            price = df.iloc[idx]['close']
            rsi = df.iloc[idx].get('RSI', 'N/A')
            print(f"  {timestamp.strftime('%m-%d %H:%M')}: {prev} -> {curr} (价格:{price:.0f}, RSI:{rsi:.1f})")
        
        # 检查强烈信号的条件
        strong_buy_data = df[df['trade_suggestion'] == '强烈做多']
        strong_sell_data = df[df['trade_suggestion'] == '强烈做空']
        
        if len(strong_buy_data) > 0:
            avg_rsi_strong_buy = strong_buy_data['RSI'].mean()
            print(f"\n强烈做多信号的平均RSI: {avg_rsi_strong_buy:.1f}")
        
        if len(strong_sell_data) > 0:
            avg_rsi_strong_sell = strong_sell_data['RSI'].mean()
            print(f"强烈做空信号的平均RSI: {avg_rsi_strong_sell:.1f}")
        
        return True
        
    except Exception as e:
        print(f"❌ 逻辑测试失败: {e}")
        return False

def test_signal_mapping():
    """测试中英文信号映射"""
    print("\n测试中英文信号映射...")
    
    expected_mapping = {
        '强烈做多': 'STRONG BUY',
        '做多': 'BUY',
        '观望': 'HOLD',
        '做空': 'SELL',
        '强烈做空': 'STRONG SELL'
    }
    
    try:
        from technical_analysis import calculate_indicators
        
        # 创建测试数据
        df = create_test_data()
        df = calculate_indicators(df)
        
        # 检查映射是否正确
        mapping_correct = True
        for idx in df.index:
            chinese = df.loc[idx, 'trade_suggestion']
            english = df.loc[idx, 'trade_suggestion_en']
            expected_english = expected_mapping.get(chinese, 'UNKNOWN')
            
            if english != expected_english:
                print(f"❌ 映射错误: {chinese} -> {english} (期望: {expected_english})")
                mapping_correct = False
                break
        
        if mapping_correct:
            print("✅ 中英文信号映射完全正确")
            
            # 显示映射示例
            print("映射关系:")
            for chinese, english in expected_mapping.items():
                print(f"  {chinese} <-> {english}")
        
        return mapping_correct
        
    except Exception as e:
        print(f"❌ 映射测试失败: {e}")
        return False

def show_signal_criteria():
    """显示信号判断标准"""
    print("\n📋 5种交易信号的判断标准:")
    print("=" * 50)
    
    criteria = {
        "强烈做多": [
            "综合评分 >= 6分",
            "接近关键支撑位 (+3分)",
            "RSI严重超卖 (<25) (+2分)", 
            "MACD金叉 (+2分)",
            "多头排列 (+2分)",
            "强趋势+上升 (+1分)",
            "放量上涨 (+1分)"
        ],
        "做多": [
            "综合评分 3-5分",
            "轻微接近支撑位 (+2分)",
            "RSI超卖 (25-35) (+1分)",
            "MACD上升 (+1分)",
            "部分多头信号"
        ],
        "观望": [
            "综合评分 -2到+2分",
            "价格在支撑阻力位中间",
            "RSI在正常区间 (35-65)",
            "技术指标信号不明确"
        ],
        "做空": [
            "综合评分 -3到-5分",
            "轻微接近阻力位 (-2分)",
            "RSI超买 (65-75) (-1分)",
            "MACD下降 (-1分)",
            "部分空头信号"
        ],
        "强烈做空": [
            "综合评分 <= -6分",
            "接近关键阻力位 (-3分)",
            "RSI严重超买 (>75) (-2分)",
            "MACD死叉 (-2分)",
            "空头排列 (-2分)",
            "强趋势+下降 (-1分)",
            "放量下跌 (-1分)"
        ]
    }
    
    for signal, conditions in criteria.items():
        print(f"\n🎯 {signal}:")
        for condition in conditions:
            print(f"   • {condition}")

def main():
    """主测试函数"""
    print("=" * 60)
    print("5种交易信号功能测试")
    print("=" * 60)
    
    # 显示判断标准
    show_signal_criteria()
    
    print("\n" + "=" * 60)
    print("功能测试")
    print("=" * 60)
    
    # 测试各个功能
    distribution_ok = test_signal_distribution()
    logic_ok = test_signal_logic()
    mapping_ok = test_signal_mapping()
    
    print("\n" + "=" * 60)
    print("测试总结:")
    print("=" * 60)
    
    if distribution_ok and logic_ok and mapping_ok:
        print("✅ 所有测试通过！")
        print("✅ 5种交易信号功能正常")
        print("✅ 信号逻辑合理，映射正确")
    else:
        print("❌ 部分测试失败")
    
    print("\n🎯 新的5种交易信号:")
    print("1. 强烈做多 - 多个强烈看涨信号汇聚")
    print("2. 做多 - 温和看涨信号")
    print("3. 观望 - 信号不明确，等待机会")
    print("4. 做空 - 温和看跌信号") 
    print("5. 强烈做空 - 多个强烈看跌信号汇聚")

if __name__ == "__main__":
    main()
