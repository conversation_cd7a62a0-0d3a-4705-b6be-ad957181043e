# test_five_signals.py
"""
测试基于TA-Lib的精确5种交易信号功能
"""
import pandas as pd
import numpy as np
from datetime import datetime, timedelta

def create_test_data():
    """创建测试数据"""
    dates = pd.date_range(start=datetime.now() - timedelta(hours=200), 
                         end=datetime.now(), freq='H')
    
    np.random.seed(42)
    
    # 创建有趋势的价格数据
    base_price = 60000
    trend = np.linspace(0, 2000, len(dates))  # 上升趋势
    noise = np.random.randn(len(dates)) * 200
    prices = base_price + trend + noise
    
    data = {
        'open': prices + np.random.randn(len(dates)) * 50,
        'high': prices + np.abs(np.random.randn(len(dates))) * 100,
        'low': prices - np.abs(np.random.randn(len(dates))) * 100,
        'close': prices,
        'volume': np.random.randint(500, 2000, len(dates)),
        'pct_change': np.random.randn(len(dates)) * 2,
        'TR': np.random.rand(len(dates)) * 100
    }
    
    df = pd.DataFrame(data, index=dates)
    
    # 确保OHLC逻辑正确
    for i in range(len(df)):
        df.iloc[i, df.columns.get_loc('high')] = max(df.iloc[i]['open'], df.iloc[i]['close'], df.iloc[i]['high'])
        df.iloc[i, df.columns.get_loc('low')] = min(df.iloc[i]['open'], df.iloc[i]['close'], df.iloc[i]['low'])
    
    return df

def test_signal_distribution():
    """测试交易信号分布"""
    print("测试5种交易信号分布...")
    
    try:
        from technical_analysis import calculate_indicators
        
        # 创建测试数据
        df = create_test_data()
        df = calculate_indicators(df)
        
        # 统计信号分布
        signal_counts = df['trade_suggestion'].value_counts()
        signal_en_counts = df['trade_suggestion_en'].value_counts()
        
        print("中文交易信号分布:")
        for signal, count in signal_counts.items():
            percentage = (count / len(df)) * 100
            print(f"  {signal}: {count}次 ({percentage:.1f}%)")
        
        print("\n英文交易信号分布:")
        for signal, count in signal_en_counts.items():
            percentage = (count / len(df)) * 100
            print(f"  {signal}: {count}次 ({percentage:.1f}%)")
        
        # 检查是否有5种信号
        expected_signals = ['强烈做多', '做多', '观望', '做空', '强烈做空']
        actual_signals = set(signal_counts.keys())
        
        print(f"\n期望的5种信号: {expected_signals}")
        print(f"实际出现的信号: {list(actual_signals)}")
        
        has_all_signals = all(signal in actual_signals for signal in expected_signals)
        print(f"✅ 包含所有5种信号: {has_all_signals}")
        
        return has_all_signals
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_signal_logic():
    """测试信号逻辑的合理性"""
    print("\n测试信号逻辑合理性...")
    
    try:
        from technical_analysis import calculate_indicators
        
        # 创建测试数据
        df = create_test_data()
        df = calculate_indicators(df)
        
        # 分析信号转换
        signal_changes = []
        for i in range(1, len(df)):
            prev_signal = df.iloc[i-1]['trade_suggestion']
            curr_signal = df.iloc[i]['trade_suggestion']
            if prev_signal != curr_signal:
                signal_changes.append((i, prev_signal, curr_signal))
        
        print(f"信号变化次数: {len(signal_changes)}")
        
        # 显示前几次信号变化
        print("前10次信号变化:")
        for i, (idx, prev, curr) in enumerate(signal_changes[:10]):
            timestamp = df.index[idx]
            price = df.iloc[idx]['close']
            rsi = df.iloc[idx].get('RSI', 'N/A')
            print(f"  {timestamp.strftime('%m-%d %H:%M')}: {prev} -> {curr} (价格:{price:.0f}, RSI:{rsi:.1f})")
        
        # 检查强烈信号的条件
        strong_buy_data = df[df['trade_suggestion'] == '强烈做多']
        strong_sell_data = df[df['trade_suggestion'] == '强烈做空']
        
        if len(strong_buy_data) > 0:
            avg_rsi_strong_buy = strong_buy_data['RSI'].mean()
            print(f"\n强烈做多信号的平均RSI: {avg_rsi_strong_buy:.1f}")
        
        if len(strong_sell_data) > 0:
            avg_rsi_strong_sell = strong_sell_data['RSI'].mean()
            print(f"强烈做空信号的平均RSI: {avg_rsi_strong_sell:.1f}")
        
        return True
        
    except Exception as e:
        print(f"❌ 逻辑测试失败: {e}")
        return False

def test_signal_mapping():
    """测试中英文信号映射"""
    print("\n测试中英文信号映射...")
    
    expected_mapping = {
        '强烈做多': 'STRONG BUY',
        '做多': 'BUY',
        '观望': 'HOLD',
        '做空': 'SELL',
        '强烈做空': 'STRONG SELL'
    }
    
    try:
        from technical_analysis import calculate_indicators
        
        # 创建测试数据
        df = create_test_data()
        df = calculate_indicators(df)
        
        # 检查映射是否正确
        mapping_correct = True
        for idx in df.index:
            chinese = df.loc[idx, 'trade_suggestion']
            english = df.loc[idx, 'trade_suggestion_en']
            expected_english = expected_mapping.get(chinese, 'UNKNOWN')
            
            if english != expected_english:
                print(f"❌ 映射错误: {chinese} -> {english} (期望: {expected_english})")
                mapping_correct = False
                break
        
        if mapping_correct:
            print("✅ 中英文信号映射完全正确")
            
            # 显示映射示例
            print("映射关系:")
            for chinese, english in expected_mapping.items():
                print(f"  {chinese} <-> {english}")
        
        return mapping_correct
        
    except Exception as e:
        print(f"❌ 映射测试失败: {e}")
        return False

def show_signal_criteria():
    """显示基于TA-Lib的精确信号判断标准"""
    print("\n📋 基于TA-Lib的精确5种交易信号判断标准:")
    print("=" * 60)

    criteria = {
        "强烈做多 (≥15分)": [
            "🔥 趋势分析 (30%权重):",
            "  • 完美多头排列: SMA20>SMA50>SMA200 (+5分)",
            "  • EMA金叉: EMA12上穿EMA26 (+3分)",
            "  • ADX>25且DI+>DI- (+2分)",
            "  • SAR支撑 (+2分)",
            "",
            "📈 动量分析 (25%权重):",
            "  • RSI<20严重超卖 (+4分)",
            "  • 威廉指标<-80 (+2分)",
            "  • CCI<-100 (+2分)",
            "  • 随机指标<20 (+2分)",
            "  • 终极振荡器<30 (+2分)",
            "",
            "⚡ MACD分析 (20%权重):",
            "  • 零轴上方金叉 (+4分)",
            "  • 零轴下方金叉 (+3分)",
            "  • 柱状图上升 (+1分)",
            "",
            "🎯 支撑阻力 (15%权重):",
            "  • 距离支撑位<1% (+4分)",
            "  • 突破阻力位 (+3分)",
            "",
            "📊 成交量 (10%权重):",
            "  • 放量上涨 (+2分)",
            "  • OBV量价齐升 (+1分)"
        ],

        "做多 (8-14分)": [
            "温和看涨信号，部分技术指标支持上涨"
        ],

        "观望 (-7到+7分)": [
            "技术指标信号不明确，等待更清晰的方向"
        ],

        "做空 (-8到-14分)": [
            "温和看跌信号，部分技术指标支持下跌"
        ],

        "强烈做空 (≤-15分)": [
            "🔥 趋势分析 (30%权重):",
            "  • 完美空头排列: SMA20<SMA50<SMA200 (-5分)",
            "  • EMA死叉: EMA12下穿EMA26 (-3分)",
            "  • ADX>25且DI->DI+ (-2分)",
            "  • SAR阻力 (-2分)",
            "",
            "📉 动量分析 (25%权重):",
            "  • RSI>80严重超买 (-4分)",
            "  • 威廉指标>-20 (-2分)",
            "  • CCI>100 (-2分)",
            "  • 随机指标>80 (-2分)",
            "  • 终极振荡器>70 (-2分)",
            "",
            "⚡ MACD分析 (20%权重):",
            "  • 零轴上方死叉 (-3分)",
            "  • 零轴下方死叉 (-4分)",
            "  • 柱状图下降 (-1分)",
            "",
            "🎯 支撑阻力 (15%权重):",
            "  • 距离阻力位<1% (-4分)",
            "  • 跌破支撑位 (-3分)",
            "",
            "📊 成交量 (10%权重):",
            "  • 放量下跌 (-2分)",
            "  • OBV量价齐跌 (-1分)"
        ]
    }

    for signal, conditions in criteria.items():
        print(f"\n🎯 {signal}:")
        for condition in conditions:
            if condition:  # 跳过空行
                print(f"   {condition}")

def test_signal_details():
    """测试信号详细评分"""
    print("\n测试信号详细评分...")

    try:
        from technical_analysis import calculate_indicators

        # 创建测试数据
        df = create_test_data()
        df = calculate_indicators(df)

        # 检查是否有评分数据
        if 'signal_score' in df.columns:
            print("✅ 信号评分计算成功")

            # 显示最近几个信号的详细信息
            print("\n最近10个信号详情:")
            recent_data = df.tail(10)

            for idx, row in recent_data.iterrows():
                signal = row['trade_suggestion']
                score = row['signal_score']
                price = row['close']
                rsi = row.get('RSI', 'N/A')

                print(f"  {idx.strftime('%m-%d %H:%M')}: {signal:8s} (评分:{score:+3.0f}) "
                      f"价格:{price:7.0f} RSI:{rsi:5.1f}")

            # 统计各信号的平均评分
            print("\n各信号平均评分:")
            for signal in ['强烈做多', '做多', '观望', '做空', '强烈做空']:
                signal_data = df[df['trade_suggestion'] == signal]
                if len(signal_data) > 0:
                    avg_score = signal_data['signal_score'].mean()
                    count = len(signal_data)
                    print(f"  {signal}: 平均{avg_score:+5.1f}分 ({count}次)")

            return True
        else:
            print("❌ 未找到信号评分数据")
            return False

    except Exception as e:
        print(f"❌ 详细评分测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("=" * 60)
    print("5种交易信号功能测试")
    print("=" * 60)
    
    # 显示判断标准
    show_signal_criteria()
    
    print("\n" + "=" * 60)
    print("功能测试")
    print("=" * 60)
    
    # 测试各个功能
    distribution_ok = test_signal_distribution()
    logic_ok = test_signal_logic()
    mapping_ok = test_signal_mapping()
    details_ok = test_signal_details()
    
    print("\n" + "=" * 60)
    print("测试总结:")
    print("=" * 60)
    
    if distribution_ok and logic_ok and mapping_ok and details_ok:
        print("✅ 所有测试通过！")
        print("✅ 基于TA-Lib的精确5种交易信号功能正常")
        print("✅ 信号逻辑合理，评分准确，映射正确")
    else:
        print("❌ 部分测试失败")
    
    print("\n🎯 基于TA-Lib的精确5种交易信号:")
    print("1. 强烈做多 (≥15分) - 多个TA-Lib指标强烈看涨信号汇聚")
    print("2. 做多 (8-14分) - TA-Lib指标温和看涨信号")
    print("3. 观望 (-7到+7分) - TA-Lib指标信号不明确，等待机会")
    print("4. 做空 (-8到-14分) - TA-Lib指标温和看跌信号")
    print("5. 强烈做空 (≤-15分) - 多个TA-Lib指标强烈看跌信号汇聚")
    print("\n💡 使用的TA-Lib指标:")
    print("RSI, MACD, 威廉指标, CCI, 随机指标, 终极振荡器, ADX, SAR, OBV等")

if __name__ == "__main__":
    main()
