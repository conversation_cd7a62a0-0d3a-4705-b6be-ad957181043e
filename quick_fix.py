# quick_fix.py
"""
快速修复依赖问题
"""
import subprocess
import sys

def install_binance():
    """安装python-binance"""
    print("🔧 正在安装 python-binance...")
    
    commands = [
        [sys.executable, "-m", "pip", "install", "python-binance"],
        [sys.executable, "-m", "pip", "install", "-i", "https://pypi.tuna.tsinghua.edu.cn/simple", "python-binance"],
        [sys.executable, "-m", "pip", "install", "--user", "python-binance"]
    ]
    
    for i, cmd in enumerate(commands, 1):
        try:
            print(f"尝试方法 {i}...")
            subprocess.check_call(cmd)
            print("✅ python-binance 安装成功！")
            return True
        except subprocess.CalledProcessError:
            print(f"❌ 方法 {i} 失败")
            continue
    
    print("❌ 所有安装方法都失败了")
    return False

def test_import():
    """测试导入"""
    print("\n🧪 测试导入...")
    
    try:
        from binance.client import Client
        print("✅ binance.client.Client 导入成功")
        return True
    except ImportError:
        try:
            from binance import Client
            print("✅ binance.Client 导入成功")
            return True
        except ImportError:
            print("❌ 仍然无法导入 binance")
            return False

def main():
    """主函数"""
    print("🚀 快速修复 python-binance 导入问题")
    print("=" * 50)
    
    # 先测试是否已经可以导入
    if test_import():
        print("✅ python-binance 已经可以正常使用！")
        print("现在可以运行: python main.py")
        return
    
    # 尝试安装
    if install_binance():
        # 再次测试
        if test_import():
            print("\n✅ 修复成功！现在可以运行: python main.py")
        else:
            print("\n❌ 安装成功但仍无法导入，可能需要重启Python环境")
    else:
        print("\n❌ 自动安装失败，请手动安装:")
        print("pip install python-binance")
        print("或者:")
        print("conda install -c conda-forge python-binance")

if __name__ == "__main__":
    main()
