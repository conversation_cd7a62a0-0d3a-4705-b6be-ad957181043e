# data_fetcher.py
from binance import Client
import pandas as pd
import numpy as np
from config import BINANCE_API_KEY, BINANCE_API_SECRET, SYMBOL, INTERVAL, LOOKBACK_PERIOD


def fetch_klines():
    """从Binance获取K线数据（优化版）"""
    client = Client(BINANCE_API_KEY, BINANCE_API_SECRET)

    # 获取K线数据（增加10%冗余确保足够数据点）
    klines = client.get_klines(
        symbol=SYMBOL,
        interval=INTERVAL,
        limit=int(LOOKBACK_PERIOD * 1.1)
    )

    # 转换为DataFrame
    df = pd.DataFrame(klines, columns=[
        'timestamp', 'open', 'high', 'low', 'close', 'volume',
        'close_time', 'quote_asset_volume', 'number_of_trades',
        'taker_buy_base', 'taker_buy_quote', 'ignore'
    ])

    # 转换数据类型
    numeric_cols = ['open', 'high', 'low', 'close', 'volume']
    df[numeric_cols] = df[numeric_cols].apply(pd.to_numeric, axis=1)

    # 时间处理
    df['timestamp'] = pd.to_datetime(df['timestamp'], unit='ms')
    df.set_index('timestamp', inplace=True)

    # 计算价格变动百分比
    df['pct_change'] = df['close'].pct_change() * 100

    # 计算真实波幅
    df['TR'] = np.maximum(
        df['high'] - df['low'],
        np.maximum(
            abs(df['high'] - df['close'].shift(1)),
            abs(df['low'] - df['close'].shift(1))
        )
    )

    return df[['open', 'high', 'low', 'close', 'volume', 'pct_change', 'TR']]