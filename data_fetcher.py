# data_fetcher.py
from binance import Client
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from config import BINANCE_API_KEY, BINANCE_API_SECRET, SYMBOL, INTERVAL, DAYS_TO_ANALYZE

def fetch_klines():
    """获取最近5天的1小时K线数据"""
    client = Client(BINANCE_API_KEY, BINANCE_API_SECRET)
    
    # 计算时间范围
    end_time = datetime.now()
    start_time = end_time - timedelta(days=DAYS_TO_ANALYZE)
    
    # 获取K线数据
    klines = client.get_historical_klines(
        symbol=SYMBOL,
        interval=INTERVAL,
        start_str=start_time.strftime('%d %b %Y %H:%M:%S'),
        end_str=end_time.strftime('%d %b %Y %H:%M:%S')
    )
    
    # 转换为DataFrame
    df = pd.DataFrame(klines, columns=[
        'timestamp', 'open', 'high', 'low', 'close', 'volume',
        'close_time', 'quote_asset_volume', 'number_of_trades',
        'taker_buy_base', 'taker_buy_quote', 'ignore'
    ])
    
    # 转换数据类型
    numeric_cols = ['open', 'high', 'low', 'close', 'volume']
    df[numeric_cols] = df[numeric_cols].apply(pd.to_numeric, axis=1)
    
    # 时间处理
    df['timestamp'] = pd.to_datetime(df['timestamp'], unit='ms')
    df.set_index('timestamp', inplace=True)
    
    # 计算价格变动百分比
    df['pct_change'] = df['close'].pct_change() * 100
    
    # 计算真实波幅
    df['TR'] = np.maximum(
        df['high'] - df['low'],
        np.maximum(
            abs(df['high'] - df['close'].shift(1)),
            abs(df['low'] - df['close'].shift(1))
    )
    
    return df[['open', 'high', 'low', 'close', 'volume', 'pct_change', 'TR']]