# data_fetcher.py
try:
    from binance.client import Client
except ImportError:
    try:
        from binance import Client
    except ImportError:
        print("❌ 缺少 python-binance 库，请运行: pip install python-binance")
        Client = None
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from config import BINANCE_API_KEY, BINANCE_API_SECRET, SYMBOL, INTERVAL, DAYS_TO_ANALYZE, LOOKBACK_PERIOD

def fetch_klines():
    """获取最近5天的1小时K线数据"""
    if Client is None:
        print("❌ 无法导入 Binance 客户端，请安装依赖: pip install python-binance")
        return None

    try:
        client = Client(BINANCE_API_KEY, BINANCE_API_SECRET)
    except Exception as e:
        print(f"❌ 创建 Binance 客户端失败: {e}")
        return None
    
    # 计算时间范围
    end_time = datetime.now()
    start_time = end_time - timedelta(days=DAYS_TO_ANALYZE)
    
    # 获取K线数据
    klines = client.get_historical_klines(
        symbol=SYMBOL,
        interval=INTERVAL,
        start_str=start_time.strftime('%d %b %Y %H:%M:%S'),
        end_str=end_time.strftime('%d %b %Y %H:%M:%S')
    )
    
    # 转换为DataFrame
    df = pd.DataFrame(klines, columns=[
        'timestamp', 'open', 'high', 'low', 'close', 'volume',
        'close_time', 'quote_asset_volume', 'number_of_trades',
        'taker_buy_base', 'taker_buy_quote', 'ignore'
    ])
    
    # 转换数据类型
    numeric_cols = ['open', 'high', 'low', 'close', 'volume']
    df[numeric_cols] = df[numeric_cols].apply(pd.to_numeric, axis=1)
    
    # 时间处理
    df['timestamp'] = pd.to_datetime(df['timestamp'], unit='ms')
    df.set_index('timestamp', inplace=True)
    
    # 计算价格变动百分比
    df['pct_change'] = df['close'].pct_change() * 100
    
    # 计算真实波幅
    df['TR'] = np.maximum(
        df['high'] - df['low'],
        np.maximum(
            abs(df['high'] - df['close'].shift(1)),
            abs(df['low'] - df['close'].shift(1))
        )
    )
    
    # 显示数据信息
    print(f"数据时间范围: {df.index[0]} 到 {df.index[-1]}")
    print(f"最新价格: {df['close'].iloc[-1]:.2f}")

    return df[['open', 'high', 'low', 'close', 'volume', 'pct_change', 'TR']]