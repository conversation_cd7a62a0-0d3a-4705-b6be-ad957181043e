# data_fetcher.py
from binance import Client
import pandas as pd
import numpy as np
from config import BINANCE_API_KEY, BINANCE_API_SECRET, SYMBOL, INTERVAL, LOOKBACK_PERIOD


def fetch_klines():
    """从Binance获取最新的K线数据"""
    client = Client(BINANCE_API_KEY, BINANCE_API_SECRET)

    # 获取最新的K线数据
    # limit参数确保获取足够的历史数据来计算技术指标
    klines = client.get_klines(
        symbol=SYMBOL,
        interval=INTERVAL,
        limit=LOOKBACK_PERIOD  # 直接使用配置的数量
    )

    print(f"获取到 {len(klines)} 条 {INTERVAL} K线数据")

    # 转换为DataFrame
    df = pd.DataFrame(klines, columns=[
        'timestamp', 'open', 'high', 'low', 'close', 'volume',
        'close_time', 'quote_asset_volume', 'number_of_trades',
        'taker_buy_base', 'taker_buy_quote', 'ignore'
    ])

    # 转换数据类型
    numeric_cols = ['open', 'high', 'low', 'close', 'volume']
    df[numeric_cols] = df[numeric_cols].apply(pd.to_numeric, axis=1)

    # 时间处理
    df['timestamp'] = pd.to_datetime(df['timestamp'], unit='ms')
    df.set_index('timestamp', inplace=True)

    # 计算价格变动百分比
    df['pct_change'] = df['close'].pct_change() * 100

    # 计算真实波幅
    df['TR'] = np.maximum(
        df['high'] - df['low'],
        np.maximum(
            abs(df['high'] - df['close'].shift(1)),
            abs(df['low'] - df['close'].shift(1))
        )
    )

    # 显示最新数据的时间信息
    latest_time = df.index[-1]
    print(f"最新K线时间: {latest_time}")
    print(f"最新收盘价: {df['close'].iloc[-1]:.2f}")

    return df[['open', 'high', 'low', 'close', 'volume', 'pct_change', 'TR']]


def get_latest_price():
    """获取当前最新价格"""
    client = Client(BINANCE_API_KEY, BINANCE_API_SECRET)
    ticker = client.get_symbol_ticker(symbol=SYMBOL)
    return float(ticker['price'])


def fetch_realtime_klines():
    """获取实时K线数据（包含未完成的当前K线）"""
    client = Client(BINANCE_API_KEY, BINANCE_API_SECRET)

    # 获取最新的K线数据，包括当前未完成的K线
    klines = client.get_klines(
        symbol=SYMBOL,
        interval=INTERVAL,
        limit=LOOKBACK_PERIOD + 1  # 多获取一条确保包含最新数据
    )

    print(f"获取到 {len(klines)} 条实时 {INTERVAL} K线数据")

    # 转换为DataFrame
    df = pd.DataFrame(klines, columns=[
        'timestamp', 'open', 'high', 'low', 'close', 'volume',
        'close_time', 'quote_asset_volume', 'number_of_trades',
        'taker_buy_base', 'taker_buy_quote', 'ignore'
    ])

    # 转换数据类型
    numeric_cols = ['open', 'high', 'low', 'close', 'volume']
    df[numeric_cols] = df[numeric_cols].apply(pd.to_numeric, axis=1)

    # 时间处理
    df['timestamp'] = pd.to_datetime(df['timestamp'], unit='ms')
    df.set_index('timestamp', inplace=True)

    # 计算价格变动百分比
    df['pct_change'] = df['close'].pct_change() * 100

    # 计算真实波幅
    df['TR'] = np.maximum(
        df['high'] - df['low'],
        np.maximum(
            abs(df['high'] - df['close'].shift(1)),
            abs(df['low'] - df['close'].shift(1))
        )
    )

    # 显示最新数据信息
    latest_time = df.index[-1]
    current_price = df['close'].iloc[-1]
    print(f"最新K线时间: {latest_time}")
    print(f"当前价格: {current_price:.2f}")

    # 检查最新K线是否为当前未完成的K线
    import datetime
    now = datetime.datetime.now()
    if latest_time.hour == now.hour:
        print("⚠️  注意: 最新K线可能尚未完成，数据会持续更新")

    return df[['open', 'high', 'low', 'close', 'volume', 'pct_change', 'TR']]