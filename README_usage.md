# 使用说明

## 获取最新1小时K线数据的方法

### 1. 完整分析（推荐）
```bash
python main.py
```
- 获取最新K线数据
- 计算所有技术指标
- 生成图表和分析报告
- 保存完整数据文件

### 2. 快速更新
```bash
python main.py quick
```
- 快速获取最新数据和关键指标
- 显示当前价格、RSI、MACD、支撑阻力位
- 适合频繁检查市场状态

### 3. 实时监控
```bash
python live_monitor.py
```
- 持续监控价格和技术指标
- 默认60秒更新一次
- 实时显示界面，按Ctrl+C停止

```bash
python live_monitor.py monitor 30
```
- 自定义更新间隔（30秒）

### 4. 快速检查
```bash
python live_monitor.py quick
```
- 一次性显示当前关键信息

## 数据获取特点

1. **实时性**: 使用 `fetch_realtime_klines()` 获取包含当前未完成K线的最新数据
2. **准确性**: 自动显示最新K线时间和当前价格
3. **完整性**: 获取足够的历史数据（200条）确保技术指标计算准确
4. **提示性**: 会提示最新K线是否为未完成状态

## 技术指标说明

系统会自动计算以下指标：
- 移动平均线 (SMA 20/50/200, EMA 12/26)
- RSI相对强弱指数
- MACD指标
- 布林带
- ADX平均趋向指数
- ATR平均真实波幅
- 随机指标
- 支撑位和阻力位

## 注意事项

1. 确保网络连接正常
2. API密钥需要有效
3. 1小时K线的最新数据可能包含未完成的当前小时数据
4. 建议在整点后几分钟运行以获取完整的上一小时数据
