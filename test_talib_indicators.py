# test_talib_indicators.py
"""
测试TA-Lib技术指标计算
"""
import pandas as pd
import numpy as np
from datetime import datetime, timedelta

def test_all_indicators():
    """测试所有技术指标"""
    print("🔬 测试TA-Lib技术指标计算...")
    
    try:
        from technical_analysis import calculate_indicators
        from data_fetcher import fetch_klines
        
        # 获取真实数据
        print("获取Binance K线数据...")
        df = fetch_klines()
        
        if df is None or len(df) == 0:
            print("❌ 无法获取数据，使用模拟数据")
            df = create_test_data()
        else:
            print(f"✅ 获取到 {len(df)} 条真实数据")
        
        # 计算所有技术指标
        print("\n计算技术指标...")
        df = calculate_indicators(df)
        
        # 检查指标计算结果
        print(f"\n📊 技术指标计算结果:")
        print(f"数据行数: {len(df)}")
        print(f"数据列数: {len(df.columns)}")
        
        # 分类显示指标
        indicator_categories = {
            "趋势指标": [
                'SMA_5', 'SMA_10', 'SMA_20', 'SMA_50', 'SMA_200',
                'EMA_5', 'EMA_12', 'EMA_20', 'EMA_26', 'EMA_50',
                'WMA_10', 'WMA_20', 'TEMA_20', 'TRIMA_20', 'KAMA',
                'MAMA', 'FAMA', 'SAR', 'ADX', 'DI_plus', 'DI_minus', 'ADXR'
            ],
            "动量指标": [
                'RSI', 'STOCH_K', 'STOCH_D', 'WILLR', 'CCI', 'MOM', 'ROC',
                'ULTOSC', 'CMO', 'PPO', 'TRIX'
            ],
            "MACD指标": [
                'MACD', 'MACD_signal', 'MACD_hist'
            ],
            "布林带指标": [
                'BB_upper', 'BB_middle', 'BB_lower', 'BB_width', 'BB_percent'
            ],
            "波动率指标": [
                'ATR', 'TRANGE', 'STDDEV', 'VAR'
            ],
            "成交量指标": [
                'OBV', 'AD', 'ADOSC', 'VOL_SMA_10', 'VOL_SMA_20', 'VOL_EMA_10', 'VOL_ratio'
            ],
            "价格指标": [
                'MEDPRICE', 'TYPPRICE', 'WCLPRICE', 'AVGPRICE', 'pct_change', 'price_change'
            ],
            "希尔伯特变换": [
                'HT_DCPERIOD', 'HT_DCPHASE', 'HT_PHASOR_inphase', 'HT_PHASOR_quadrature',
                'HT_SINE_sine', 'HT_SINE_leadsine', 'HT_TRENDMODE'
            ],
            "线性回归": [
                'LINEARREG', 'LINEARREG_ANGLE', 'LINEARREG_INTERCEPT', 'LINEARREG_SLOPE', 'TSF'
            ]
        }
        
        total_indicators = 0
        calculated_indicators = 0
        
        for category, indicators in indicator_categories.items():
            print(f"\n📈 {category}:")
            category_count = 0
            for indicator in indicators:
                total_indicators += 1
                if indicator in df.columns:
                    non_na_count = df[indicator].notna().sum()
                    if non_na_count > 0:
                        calculated_indicators += 1
                        category_count += 1
                        latest_value = df[indicator].iloc[-1]
                        if pd.notna(latest_value):
                            print(f"  ✅ {indicator}: {non_na_count}/{len(df)} 有效值, 最新值: {latest_value:.4f}")
                        else:
                            print(f"  ⚠️  {indicator}: {non_na_count}/{len(df)} 有效值, 最新值: N/A")
                    else:
                        print(f"  ❌ {indicator}: 计算失败")
                else:
                    print(f"  ❌ {indicator}: 未找到")
            
            print(f"    {category} 成功计算: {category_count}/{len(indicators)} 个指标")
        
        print(f"\n📊 总体统计:")
        print(f"总指标数: {total_indicators}")
        print(f"成功计算: {calculated_indicators}")
        print(f"成功率: {(calculated_indicators/total_indicators)*100:.1f}%")
        
        # 显示最新数据
        print(f"\n📈 最新数据点 ({df.index[-1]}):")
        latest = df.iloc[-1]
        key_indicators = ['close', 'RSI', 'MACD', 'BB_percent', 'ADX', 'SAR', 'OBV']
        
        for indicator in key_indicators:
            if indicator in df.columns and pd.notna(latest[indicator]):
                print(f"  {indicator}: {latest[indicator]:.4f}")
        
        # 检查交易信号
        if 'trade_suggestion' in df.columns:
            print(f"\n🎯 交易信号分析:")
            signal_counts = df['trade_suggestion'].value_counts()
            for signal, count in signal_counts.items():
                percentage = (count / len(df)) * 100
                print(f"  {signal}: {count}次 ({percentage:.1f}%)")
            
            print(f"\n最新交易信号: {latest.get('trade_suggestion', 'N/A')}")
            if 'signal_score' in df.columns:
                print(f"信号评分: {latest.get('signal_score', 0):.1f}")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def create_test_data():
    """创建测试数据"""
    dates = pd.date_range(start=datetime.now() - timedelta(hours=200), 
                         end=datetime.now(), freq='H')
    
    np.random.seed(42)
    base_price = 60000
    trend = np.cumsum(np.random.randn(len(dates)) * 50)
    prices = base_price + trend
    
    data = {
        'open': prices + np.random.randn(len(dates)) * 20,
        'high': prices + np.abs(np.random.randn(len(dates))) * 50,
        'low': prices - np.abs(np.random.randn(len(dates))) * 50,
        'close': prices,
        'volume': np.random.randint(500, 2000, len(dates))
    }
    
    df = pd.DataFrame(data, index=dates)
    
    # 确保OHLC逻辑正确
    for i in range(len(df)):
        df.iloc[i, df.columns.get_loc('high')] = max(df.iloc[i]['open'], df.iloc[i]['close'], df.iloc[i]['high'])
        df.iloc[i, df.columns.get_loc('low')] = min(df.iloc[i]['open'], df.iloc[i]['close'], df.iloc[i]['low'])
    
    return df

def test_specific_indicators():
    """测试特定指标的准确性"""
    print(f"\n🎯 测试特定指标准确性...")
    
    try:
        from technical_analysis import calculate_indicators
        
        # 创建简单测试数据
        dates = pd.date_range(start='2025-01-01', periods=100, freq='H')
        prices = [100 + i + np.sin(i/10)*5 for i in range(100)]
        
        df = pd.DataFrame({
            'open': prices,
            'high': [p + 2 for p in prices],
            'low': [p - 2 for p in prices],
            'close': prices,
            'volume': [1000] * 100
        }, index=dates)
        
        df = calculate_indicators(df)
        
        # 验证关键指标
        tests = []
        
        # RSI应该在0-100之间
        if 'RSI' in df.columns:
            rsi_valid = df['RSI'].dropna()
            rsi_in_range = ((rsi_valid >= 0) & (rsi_valid <= 100)).all()
            tests.append(('RSI范围检查', rsi_in_range))
        
        # 布林带上轨应该大于下轨
        if all(col in df.columns for col in ['BB_upper', 'BB_lower']):
            bb_valid = (df['BB_upper'] > df['BB_lower']).all()
            tests.append(('布林带逻辑检查', bb_valid))
        
        # MACD柱状图应该等于MACD减去信号线
        if all(col in df.columns for col in ['MACD', 'MACD_signal', 'MACD_hist']):
            macd_diff = df['MACD'] - df['MACD_signal']
            macd_hist_correct = np.allclose(df['MACD_hist'].dropna(), 
                                          macd_diff.dropna(), rtol=1e-10)
            tests.append(('MACD柱状图计算', macd_hist_correct))
        
        # 显示测试结果
        print(f"指标准确性测试:")
        for test_name, result in tests:
            status = "✅" if result else "❌"
            print(f"  {status} {test_name}: {'通过' if result else '失败'}")
        
        return all(result for _, result in tests)
        
    except Exception as e:
        print(f"❌ 特定指标测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🔬 TA-Lib技术指标完整测试")
    print("=" * 60)
    
    # 测试所有指标
    all_indicators_ok = test_all_indicators()
    
    # 测试特定指标准确性
    specific_tests_ok = test_specific_indicators()
    
    print(f"\n" + "=" * 60)
    print("测试总结:")
    print("=" * 60)
    
    if all_indicators_ok and specific_tests_ok:
        print("✅ 所有测试通过！")
        print("✅ TA-Lib技术指标计算正确")
        print("✅ 包含完整的RSI、MA、EMA、BOLL、SAR、ADX、VOL、MACD等指标")
    else:
        print("⚠️ 部分测试失败")
        if not all_indicators_ok:
            print("❌ 指标计算测试失败")
        if not specific_tests_ok:
            print("❌ 指标准确性测试失败")
    
    print(f"\n🎯 支持的指标类别:")
    print("• 趋势指标: SMA, EMA, WMA, TEMA, SAR, ADX等")
    print("• 动量指标: RSI, STOCH, WILLR, CCI, MOM, ROC等")
    print("• MACD系列: MACD, 信号线, 柱状图")
    print("• 布林带: 上轨, 中轨, 下轨, 宽度, 位置")
    print("• 波动率: ATR, 标准差, 方差")
    print("• 成交量: OBV, A/D线, 成交量均线")
    print("• 价格: 典型价格, 加权价格, 中位价格")
    print("• 高级: 希尔伯特变换, 线性回归等")
    
    print(f"\n🚀 现在可以运行 python main.py 获取完整的技术分析！")

if __name__ == "__main__":
    main()
