# test_csv_format.py
"""
测试CSV文件的时间格式
"""
import pandas as pd
import numpy as np
from datetime import datetime, timedelta

def create_test_data():
    """创建测试数据"""
    # 生成24小时的测试数据
    dates = pd.date_range(start=datetime.now() - timedelta(hours=24), 
                         end=datetime.now(), freq='H')
    
    # 生成模拟数据
    np.random.seed(42)
    data = {
        'open': 60000 + np.random.randn(len(dates)) * 100,
        'high': 60000 + np.random.randn(len(dates)) * 100 + 50,
        'low': 60000 + np.random.randn(len(dates)) * 100 - 50,
        'close': 60000 + np.random.randn(len(dates)) * 100,
        'volume': np.random.randint(100, 1000, len(dates)),
        'pct_change': np.random.randn(len(dates)) * 2,
        'RSI': 50 + np.random.randn(len(dates)) * 15,
        'MACD': np.random.randn(len(dates)) * 10,
        'MACD_signal': np.random.randn(len(dates)) * 8,
        'key_support': 59500,
        'key_resistance': 60500,
        'trade_suggestion': np.random.choice(['买入', '卖出', '观望'], len(dates))
    }
    
    df = pd.DataFrame(data, index=dates)
    return df

def test_time_format():
    """测试时间格式"""
    print("测试CSV时间格式...")
    
    # 创建测试数据
    df = create_test_data()
    
    # 模拟report_generator中的时间格式处理
    df_clean = df.copy()
    
    # 添加时间信息列
    df_clean.insert(0, '月日', df_clean.index.strftime('%m-%d'))
    df_clean.insert(1, '时分', df_clean.index.strftime('%H:%M'))
    df_clean.insert(2, '小时', df_clean.index.hour)
    df_clean.insert(3, '星期', df_clean.index.strftime('%A'))
    
    # 显示前几行
    print("时间格式示例:")
    print(df_clean[['月日', '时分', '小时', '星期', 'close']].head(10))
    
    # 保存测试文件
    test_filename = 'test_time_format.csv'
    df_clean.to_csv(test_filename, index=False, encoding='utf-8-sig')
    
    print(f"\n✅ 测试文件已生成: {test_filename}")
    print(f"时间范围: {df_clean['月日'].iloc[0]} {df_clean['时分'].iloc[0]} 到 {df_clean['月日'].iloc[-1]} {df_clean['时分'].iloc[-1]}")
    
    return test_filename

def show_format_comparison():
    """显示格式对比"""
    now = datetime.now()
    
    print("\n时间格式对比:")
    print("=" * 50)
    print(f"原始时间: {now}")
    print(f"月日格式: {now.strftime('%m-%d')}")
    print(f"时分格式: {now.strftime('%H:%M')}")
    print(f"小时: {now.hour}")
    print(f"星期: {now.strftime('%A')}")
    print("=" * 50)
    
    print("\n优势:")
    print("✅ 月日格式简洁，便于快速识别日期")
    print("✅ 时分格式清晰，便于识别具体时间")
    print("✅ 分离的列便于数据分析和筛选")
    print("✅ 减少了冗余的年份信息")

def main():
    """主测试函数"""
    print("CSV时间格式测试")
    print("=" * 50)
    
    # 显示格式对比
    show_format_comparison()
    
    # 测试时间格式
    test_filename = test_time_format()
    
    print(f"\n💡 现在CSV文件将包含:")
    print("- 第1列: 月日 (MM-DD)")
    print("- 第2列: 时分 (HH:MM)")
    print("- 第3列: 小时 (数字)")
    print("- 第4列: 星期 (英文)")
    print("- 后续列: 价格和技术指标数据")
    
    print(f"\n🎯 这样的格式更适合:")
    print("- AI快速理解时间信息")
    print("- 人工阅读和分析")
    print("- 数据筛选和排序")

if __name__ == "__main__":
    main()
