# test_csv_format.py
"""
测试CSV文件格式和大小问题
"""
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import os

def create_test_data():
    """创建测试数据"""
    print("创建测试数据...")
    
    # 生成120小时的测试数据（5天）
    dates = pd.date_range(start=datetime.now() - timedelta(hours=120), 
                         end=datetime.now(), freq='H')
    
    # 生成模拟的OHLCV数据
    np.random.seed(42)  # 固定随机种子
    base_price = 60000
    
    data = {
        'DateTime': dates.strftime('%Y-%m-%d %H:%M:%S'),
        'Date': dates.strftime('%Y-%m-%d'),
        'Hour': dates.hour,
        'Open_Price': base_price + np.random.randn(len(dates)) * 1000,
        'High_Price': base_price + np.random.randn(len(dates)) * 1000 + 500,
        'Low_Price': base_price + np.random.randn(len(dates)) * 1000 - 500,
        'Close_Price': base_price + np.random.randn(len(dates)) * 1000,
        'Volume': np.random.randint(100, 10000, len(dates)),
        'Price_Change_Pct': np.random.randn(len(dates)) * 2,
        'RSI_14': 50 + np.random.randn(len(dates)) * 20,
        'MACD_Line': np.random.randn(len(dates)) * 100,
        'MACD_Signal': np.random.randn(len(dates)) * 100,
        'MACD_Histogram': np.random.randn(len(dates)) * 50,
        'SMA_20_Period': base_price + np.random.randn(len(dates)) * 800,
        'SMA_50_Period': base_price + np.random.randn(len(dates)) * 600,
        'EMA_12_Period': base_price + np.random.randn(len(dates)) * 900,
        'EMA_26_Period': base_price + np.random.randn(len(dates)) * 700,
        'ATR_14': np.random.rand(len(dates)) * 1000 + 200,
        'ADX_14': np.random.rand(len(dates)) * 100,
        'Key_Support_Level': base_price - np.random.rand(len(dates)) * 2000,
        'Key_Resistance_Level': base_price + np.random.rand(len(dates)) * 2000,
        'Trade_Suggestion': np.random.choice(['BUY', 'SELL', 'HOLD'], len(dates))
    }
    
    df = pd.DataFrame(data)
    
    # 确保价格逻辑正确
    df['High_Price'] = np.maximum(df['High_Price'], df[['Open_Price', 'Close_Price']].max(axis=1))
    df['Low_Price'] = np.minimum(df['Low_Price'], df[['Open_Price', 'Close_Price']].min(axis=1))
    
    # 格式化数值
    price_cols = ['Open_Price', 'High_Price', 'Low_Price', 'Close_Price', 'Key_Support_Level', 'Key_Resistance_Level']
    for col in price_cols:
        df[col] = df[col].round(2)
    
    df['Volume'] = df['Volume'].astype(int)
    df['Price_Change_Pct'] = df['Price_Change_Pct'].round(3)
    df['RSI_14'] = np.clip(df['RSI_14'], 0, 100).round(2)
    
    indicator_cols = ['MACD_Line', 'MACD_Signal', 'MACD_Histogram', 'SMA_20_Period', 
                     'SMA_50_Period', 'EMA_12_Period', 'EMA_26_Period', 'ATR_14', 'ADX_14']
    for col in indicator_cols:
        df[col] = df[col].round(4)
    
    print(f"✅ 测试数据创建完成: {len(df)} 行 x {len(df.columns)} 列")
    return df

def test_csv_formats(df):
    """测试不同的CSV格式"""
    print("\n测试不同CSV格式...")
    
    formats = [
        {
            'name': 'standard_format',
            'params': {
                'index': False,
                'encoding': 'utf-8-sig',
                'float_format': '%.4f'
            }
        },
        {
            'name': 'compact_format', 
            'params': {
                'index': False,
                'encoding': 'utf-8',
                'float_format': '%.2f'
            }
        },
        {
            'name': 'detailed_format',
            'params': {
                'index': False,
                'encoding': 'utf-8-sig',
                'float_format': '%.6f',
                'date_format': '%Y-%m-%d %H:%M:%S'
            }
        }
    ]
    
    results = []
    
    for fmt in formats:
        filename = f"test_{fmt['name']}.csv"
        
        try:
            # 保存文件
            df.to_csv(filename, **fmt['params'])
            
            # 检查文件大小
            file_size = os.path.getsize(filename) / 1024  # KB
            
            # 测试读取
            df_read = pd.read_csv(filename, encoding='utf-8-sig')
            
            result = {
                'format': fmt['name'],
                'filename': filename,
                'size_kb': file_size,
                'rows': len(df_read),
                'cols': len(df_read.columns),
                'readable': True
            }
            
            print(f"✅ {fmt['name']}: {filename} ({file_size:.1f} KB)")
            
        except Exception as e:
            result = {
                'format': fmt['name'],
                'filename': filename,
                'error': str(e),
                'readable': False
            }
            print(f"❌ {fmt['name']}: 失败 - {e}")
        
        results.append(result)
    
    return results

def test_data_size_limits(df):
    """测试不同数据量的文件大小"""
    print("\n测试不同数据量的文件大小...")
    
    sizes = [24, 48, 72, 120, 168]  # 1天, 2天, 3天, 5天, 7天
    
    for size in sizes:
        if size <= len(df):
            df_subset = df.tail(size)
            filename = f"test_{size}hours_data.csv"
            
            df_subset.to_csv(filename, index=False, encoding='utf-8-sig', float_format='%.4f')
            
            file_size = os.path.getsize(filename) / 1024  # KB
            print(f"📊 {size}小时数据: {filename} ({file_size:.1f} KB)")
            
            # 测试AI可读性（模拟）
            if file_size < 100:  # 小于100KB通常AI可以完全读取
                readability = "完全可读"
            elif file_size < 500:  # 100-500KB可能部分读取
                readability = "部分可读"
            else:  # 大于500KB可能只读取开头和结尾
                readability = "仅读取开头/结尾"
            
            print(f"   AI可读性预估: {readability}")

def create_optimized_csv(df):
    """创建优化的CSV文件"""
    print("\n创建优化的CSV文件...")
    
    # 选择最重要的列
    important_cols = [
        'DateTime', 'Date', 'Hour',
        'Open_Price', 'High_Price', 'Low_Price', 'Close_Price', 'Volume',
        'Price_Change_Pct', 'RSI_14', 'MACD_Line', 'MACD_Signal',
        'SMA_20_Period', 'SMA_50_Period', 'EMA_12_Period',
        'Key_Support_Level', 'Key_Resistance_Level', 'Trade_Suggestion'
    ]
    
    # 只保留存在的列
    available_cols = [col for col in important_cols if col in df.columns]
    df_optimized = df[available_cols].copy()
    
    # 限制到最近7天数据
    df_optimized = df_optimized.tail(168)  # 7天 * 24小时
    
    filename = "optimized_trading_data.csv"
    df_optimized.to_csv(filename, index=False, encoding='utf-8-sig', float_format='%.4f')
    
    file_size = os.path.getsize(filename) / 1024
    print(f"✅ 优化文件已创建: {filename}")
    print(f"   - 数据行数: {len(df_optimized)}")
    print(f"   - 数据列数: {len(df_optimized.columns)}")
    print(f"   - 文件大小: {file_size:.1f} KB")
    print(f"   - 时间范围: {df_optimized['DateTime'].iloc[0]} 到 {df_optimized['DateTime'].iloc[-1]}")
    
    # 显示前几行和后几行
    print(f"\n前3行数据:")
    print(df_optimized.head(3).to_string(index=False))
    print(f"\n后3行数据:")
    print(df_optimized.tail(3).to_string(index=False))
    
    return filename

def main():
    """主测试函数"""
    print("=" * 60)
    print("CSV文件格式和大小测试")
    print("=" * 60)
    
    # 1. 创建测试数据
    df = create_test_data()
    
    # 2. 测试不同格式
    results = test_csv_formats(df)
    
    # 3. 测试不同数据量
    test_data_size_limits(df)
    
    # 4. 创建优化版本
    optimized_file = create_optimized_csv(df)
    
    print("\n" + "=" * 60)
    print("测试总结:")
    print("=" * 60)
    
    for result in results:
        if result.get('readable'):
            print(f"✅ {result['format']}: {result['size_kb']:.1f} KB")
        else:
            print(f"❌ {result['format']}: 失败")
    
    print(f"\n推荐使用: {optimized_file}")
    print("- 文件大小适中，AI可以完全读取")
    print("- 包含所有重要的交易指标")
    print("- 格式标准，易于分析")
    
    print("\n💡 解决CSV读取问题的建议:")
    print("1. 限制数据行数到168行以内（7天数据）")
    print("2. 只保留重要的列")
    print("3. 使用标准的CSV格式")
    print("4. 文件大小控制在100KB以内")
    print("=" * 60)

if __name__ == "__main__":
    main()
