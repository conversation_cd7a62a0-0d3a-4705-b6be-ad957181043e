# test_csv_content.py
"""
测试CSV文件内容，确保没有中文字符问题
"""
import pandas as pd
import numpy as np
from datetime import datetime, timedelta

def create_test_data():
    """创建测试数据"""
    dates = pd.date_range(start=datetime.now() - timedelta(hours=120), 
                         end=datetime.now(), freq='H')
    
    np.random.seed(42)
    data = {
        'open': 60000 + np.random.randn(len(dates)) * 100,
        'high': 60000 + np.random.randn(len(dates)) * 100 + 50,
        'low': 60000 + np.random.randn(len(dates)) * 100 - 50,
        'close': 60000 + np.random.randn(len(dates)) * 100,
        'volume': np.random.randint(100, 1000, len(dates)),
        'pct_change': np.random.randn(len(dates)) * 2,
        'TR': np.random.rand(len(dates)) * 100
    }
    
    df = pd.DataFrame(data, index=dates)
    return df

def test_csv_generation():
    """测试CSV文件生成"""
    print("测试CSV文件生成...")
    
    try:
        from technical_analysis import calculate_indicators
        from report_generator import save_optimized_csv
        
        # 创建测试数据
        df = create_test_data()
        df = calculate_indicators(df)
        
        # 生成CSV文件
        csv_file = save_optimized_csv(df)
        
        # 读取生成的CSV文件
        df_read = pd.read_csv(csv_file, encoding='utf-8-sig')
        
        print(f"✅ CSV文件生成成功: {csv_file}")
        print(f"数据行数: {len(df_read)}")
        print(f"数据列数: {len(df_read.columns)}")
        
        # 显示列名
        print("\n列名列表:")
        for i, col in enumerate(df_read.columns, 1):
            print(f"{i:2d}. {col}")
        
        # 检查是否有中文字符
        chinese_columns = []
        chinese_values = []
        
        for col in df_read.columns:
            # 检查列名中的中文
            if any('\u4e00' <= char <= '\u9fff' for char in col):
                chinese_columns.append(col)
            
            # 检查列值中的中文
            if df_read[col].dtype == 'object':
                for value in df_read[col].dropna().unique():
                    if any('\u4e00' <= char <= '\u9fff' for char in str(value)):
                        chinese_values.append((col, value))
        
        if chinese_columns:
            print(f"\n❌ 发现包含中文的列名: {chinese_columns}")
        else:
            print(f"\n✅ 所有列名都是英文或英文+中文组合")
        
        if chinese_values:
            print(f"\n❌ 发现包含中文的数据值:")
            for col, value in chinese_values[:5]:  # 只显示前5个
                print(f"   列 '{col}': {value}")
        else:
            print(f"\n✅ 所有数据值都是英文")
        
        # 显示前几行数据
        print(f"\n前3行数据预览:")
        print(df_read.head(3).to_string(index=False))
        
        return len(chinese_columns) == 0 and len(chinese_values) == 0
        
    except Exception as e:
        print(f"❌ CSV测试失败: {e}")
        return False

def check_specific_columns():
    """检查特定的问题列"""
    print("\n检查特定列的修复情况...")
    
    try:
        from technical_analysis import calculate_indicators
        from report_generator import save_optimized_csv
        
        # 创建测试数据
        df = create_test_data()
        df = calculate_indicators(df)
        
        # 生成CSV文件
        csv_file = save_optimized_csv(df)
        df_read = pd.read_csv(csv_file, encoding='utf-8-sig')
        
        # 检查交易信号列
        signal_cols = [col for col in df_read.columns if 'Signal' in col or 'signal' in col]
        print(f"交易信号相关列: {signal_cols}")
        
        if signal_cols:
            signal_col = signal_cols[0]
            unique_signals = df_read[signal_col].dropna().unique()
            print(f"交易信号值: {unique_signals}")
            
            # 检查是否包含中文
            has_chinese_signals = any(any('\u4e00' <= char <= '\u9fff' for char in str(signal)) 
                                    for signal in unique_signals)
            if has_chinese_signals:
                print("❌ 交易信号仍包含中文")
            else:
                print("✅ 交易信号已全部英文化")
        
        # 检查星期列
        weekday_cols = [col for col in df_read.columns if 'Weekday' in col or '星期' in col]
        print(f"星期相关列: {weekday_cols}")
        
        if weekday_cols:
            weekday_col = weekday_cols[0]
            unique_weekdays = df_read[weekday_col].dropna().unique()
            print(f"星期值: {unique_weekdays}")
            
            # 检查是否为英文星期
            english_weekdays = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday']
            is_english_weekdays = all(day in english_weekdays for day in unique_weekdays)
            if is_english_weekdays:
                print("✅ 星期显示为英文")
            else:
                print("❌ 星期显示可能有问题")
        
        return True
        
    except Exception as e:
        print(f"❌ 特定列检查失败: {e}")
        return False

def main():
    """主测试函数"""
    print("=" * 60)
    print("CSV文件中文字符修复测试")
    print("=" * 60)
    
    # 1. 测试CSV生成
    csv_ok = test_csv_generation()
    
    # 2. 检查特定列
    specific_ok = check_specific_columns()
    
    print("\n" + "=" * 60)
    print("测试总结:")
    print("=" * 60)
    
    if csv_ok and specific_ok:
        print("✅ 所有测试通过！")
        print("✅ CSV文件中文字符问题已修复")
        print("✅ 交易信号和星期都使用英文显示")
    else:
        print("❌ 部分测试失败，请检查相关问题")
    
    print("\n💡 修复内容:")
    print("- 交易信号列名: Signal_交易信号 → Signal_Trading_Signal")
    print("- 星期列名: 星期 → Weekday_星期")
    print("- 交易信号值: 中文 → 英文 (BUY, SELL, HOLD)")
    print("- 星期值: 保持英文 (Monday, Tuesday, etc.)")

if __name__ == "__main__":
    main()
