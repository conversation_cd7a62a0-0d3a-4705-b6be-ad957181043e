# run_analysis.py
"""
改进的分析启动脚本，包含更好的错误处理和用户指导
"""
import os
import sys
from datetime import datetime

def check_dependencies():
    """检查依赖包"""
    required_packages = ['binance', 'pandas', 'numpy', 'talib', 'matplotlib']
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package)
        except ImportError:
            missing_packages.append(package)
    
    if missing_packages:
        print("❌ 缺少以下依赖包:")
        for pkg in missing_packages:
            print(f"   - {pkg}")
        print("\n请运行以下命令安装:")
        print(f"pip install {' '.join(missing_packages)}")
        return False
    
    return True

def check_api_connection():
    """检查API连接"""
    try:
        from binance import Client
        from config import BINANCE_API_KEY, BINANCE_API_SECRET
        
        client = Client(BINANCE_API_KEY, BINANCE_API_SECRET)
        server_time = client.get_server_time()
        print(f"✅ Binance API连接正常")
        return True
        
    except Exception as e:
        print(f"❌ Binance API连接失败: {e}")
        print("请检查:")
        print("1. 网络连接是否正常")
        print("2. API密钥是否正确")
        print("3. 是否被防火墙阻止")
        return False

def run_analysis():
    """运行分析"""
    print("🚀 开始运行BTCUSDT技术分析...")
    print(f"⏰ 开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    try:
        # 导入并运行主程序
        from main import main
        main()
        
        print(f"\n⏰ 完成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print("🎉 分析完成！")
        
        return True
        
    except KeyboardInterrupt:
        print("\n⚠️  用户中断了程序")
        return False
    except Exception as e:
        print(f"\n❌ 分析过程中出现错误: {e}")
        print("\n调试信息:")
        import traceback
        traceback.print_exc()
        return False

def show_usage_guide():
    """显示使用指南"""
    print("\n" + "="*60)
    print("📖 使用指南")
    print("="*60)
    print("1. 确保网络连接正常")
    print("2. 检查config.py中的API密钥是否有效")
    print("3. 运行程序会生成一个分析目录，包含4个文件:")
    print("   - 主数据文件 (.csv)")
    print("   - 分析摘要 (.md)")
    print("   - 技术图表 (.png)")
    print("   - 提问指南 (.txt)")
    print("4. 将这4个文件上传到DeepSeek进行深度分析")
    print("="*60)

def main():
    """主函数"""
    print("="*60)
    print("🔍 BTCUSDT 1小时K线技术分析系统")
    print("="*60)
    
    # 1. 检查依赖
    print("1️⃣ 检查依赖包...")
    if not check_dependencies():
        return
    
    # 2. 检查API连接
    print("\n2️⃣ 检查API连接...")
    if not check_api_connection():
        return
    
    # 3. 运行分析
    print("\n3️⃣ 运行技术分析...")
    success = run_analysis()
    
    # 4. 显示结果
    if success:
        show_usage_guide()
        
        # 询问是否打开结果目录
        try:
            choice = input("\n是否打开结果目录? (y/N): ").strip().lower()
            if choice == 'y':
                analysis_dir = "BTCUSDT_1h_analysis"
                if os.path.exists(analysis_dir):
                    os.startfile(analysis_dir)  # Windows
                    print(f"📂 已打开目录: {analysis_dir}")
        except:
            pass
    else:
        print("\n💡 如果遇到问题，请:")
        print("1. 检查网络连接")
        print("2. 确认API密钥有效")
        print("3. 运行 python test_fixed_code.py 进行诊断")

if __name__ == "__main__":
    main()
