# quick_test.py
"""
快速测试恢复的图表功能
"""
import os

def test_imports():
    """测试导入"""
    try:
        from main import main
        from visualization import (plot_technical_analysis, generate_trend_volatility_chart,
                                 generate_support_resistance_chart, generate_momentum_chart)
        print("✅ 所有模块导入成功")
        return True
    except ImportError as e:
        print(f"❌ 导入失败: {e}")
        return False

def check_files():
    """检查文件"""
    required_files = ['main.py', 'config.py', 'data_fetcher.py', 
                     'technical_analysis.py', 'visualization.py', 'report_generator.py']
    
    for file in required_files:
        if not os.path.exists(file):
            print(f"❌ 缺少文件: {file}")
            return False
    
    print("✅ 所有必要文件存在")
    return True

def main():
    """主测试"""
    print("=" * 50)
    print("图表功能恢复测试")
    print("=" * 50)
    
    if not check_files():
        return
    
    if not test_imports():
        return
    
    print("\n🎯 恢复的图表功能:")
    print("1. plot_technical_analysis() - 完整技术分析图表")
    print("2. generate_trend_volatility_chart() - 趋势和波动性图表")
    print("3. generate_support_resistance_chart() - 支撑阻力位图表")
    print("4. generate_momentum_chart() - 动量指标图表")
    
    print("\n📊 现在运行 python main.py 将生成:")
    print("- 1个分析报告文件 (.txt)")
    print("- 1个数据文件 (.csv)")
    print("- 4个图表文件 (.png)")
    print("- 总共6个文件供DeepSeek分析")
    
    print("\n✅ 图表功能恢复完成！")

if __name__ == "__main__":
    main()
