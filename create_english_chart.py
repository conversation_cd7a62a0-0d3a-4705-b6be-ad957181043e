# create_english_chart.py
"""
创建纯英文技术分析图表，避免字体问题
"""
import matplotlib.pyplot as plt
import pandas as pd
import numpy as np
from datetime import datetime
import os

def setup_chart_style():
    """设置图表样式"""
    # 使用标准英文字体
    plt.rcParams['font.family'] = ['DejaVu Sans', 'Arial', 'sans-serif']
    plt.rcParams['font.size'] = 10
    plt.rcParams['axes.titlesize'] = 12
    plt.rcParams['axes.labelsize'] = 10
    plt.rcParams['legend.fontsize'] = 9
    plt.rcParams['axes.unicode_minus'] = False
    
    print("✅ Chart style configured for English fonts")

def create_technical_chart(df, symbol='BTCUSDT', interval='1h', plot_points=120):
    """创建纯英文技术分析图表"""
    
    setup_chart_style()
    
    # 准备数据
    plot_df = df.iloc[-plot_points:] if len(df) > plot_points else df
    last_row = plot_df.iloc[-1]
    
    # 创建图表
    fig, (ax1, ax2, ax3) = plt.subplots(3, 1, figsize=(16, 12), dpi=100)
    
    # 1. 价格图表
    ax1.plot(plot_df.index, plot_df['close'], label='Close Price', color='black', linewidth=2)
    
    # 添加移动平均线
    ma_colors = {'SMA_20': 'blue', 'SMA_50': 'orange', 'EMA_12': 'green', 'EMA_26': 'red'}
    for ma, color in ma_colors.items():
        if ma in plot_df.columns:
            ax1.plot(plot_df.index, plot_df[ma], label=ma, color=color, linestyle='--', alpha=0.8)
    
    # 添加支撑阻力位
    if 'key_support' in last_row and not pd.isna(last_row['key_support']):
        ax1.axhline(y=last_row['key_support'], color='green', linestyle=':', 
                   label=f'Support: ${last_row["key_support"]:.2f}', alpha=0.8)
    
    if 'key_resistance' in last_row and not pd.isna(last_row['key_resistance']):
        ax1.axhline(y=last_row['key_resistance'], color='red', linestyle=':', 
                   label=f'Resistance: ${last_row["key_resistance"]:.2f}', alpha=0.8)
    
    # 添加当前价格标注
    current_price = plot_df['close'].iloc[-1]
    ax1.text(plot_df.index[-1], current_price, f'${current_price:.2f}', 
             fontsize=10, bbox=dict(boxstyle="round,pad=0.3", facecolor="yellow", alpha=0.8))
    
    ax1.set_title(f'{symbol} {interval} Price Chart', fontsize=14, fontweight='bold')
    ax1.set_ylabel('Price (USDT)', fontsize=12)
    ax1.legend(loc='best', fontsize=9)
    ax1.grid(True, alpha=0.3)
    
    # 2. RSI图表
    if 'RSI' in plot_df.columns:
        ax2.plot(plot_df.index, plot_df['RSI'], label='RSI(14)', color='purple', linewidth=2)
        ax2.axhline(70, color='red', linestyle='--', label='Overbought (70)', alpha=0.8)
        ax2.axhline(30, color='green', linestyle='--', label='Oversold (30)', alpha=0.8)
        ax2.fill_between(plot_df.index, 30, 70, color='lightgray', alpha=0.2, label='Normal Range')
        
        # 添加当前RSI值
        current_rsi = plot_df['RSI'].iloc[-1]
        ax2.text(plot_df.index[-1], current_rsi, f'{current_rsi:.1f}', 
                fontsize=10, bbox=dict(boxstyle="round,pad=0.3", facecolor="lightblue", alpha=0.8))
        
        ax2.set_title('RSI Indicator', fontsize=12)
        ax2.set_ylabel('RSI', fontsize=12)
        ax2.set_ylim(0, 100)
        ax2.legend(loc='best', fontsize=9)
        ax2.grid(True, alpha=0.3)
    
    # 3. MACD图表
    if all(col in plot_df.columns for col in ['MACD', 'MACD_signal']):
        ax3.plot(plot_df.index, plot_df['MACD'], label='MACD', color='blue', linewidth=2)
        ax3.plot(plot_df.index, plot_df['MACD_signal'], label='Signal', color='red', linewidth=2)
        
        # MACD柱状图
        if 'MACD_hist' in plot_df.columns:
            colors = ['green' if x >= 0 else 'red' for x in plot_df['MACD_hist']]
            ax3.bar(plot_df.index, plot_df['MACD_hist'], color=colors, alpha=0.6, label='Histogram')
        
        ax3.axhline(0, color='gray', linestyle='-', alpha=0.5)
        
        # 添加当前MACD值
        current_macd = plot_df['MACD'].iloc[-1]
        ax3.text(plot_df.index[-1], current_macd, f'{current_macd:.2f}', 
                fontsize=10, bbox=dict(boxstyle="round,pad=0.3", facecolor="lightgreen", alpha=0.8))
        
        ax3.set_title('MACD Indicator', fontsize=12)
        ax3.set_ylabel('MACD', fontsize=12)
        ax3.legend(loc='best', fontsize=9)
        ax3.grid(True, alpha=0.3)
    
    # 设置x轴标签
    for ax in [ax1, ax2, ax3]:
        ax.tick_params(axis='x', rotation=45)
    
    # 总标题
    fig.suptitle(f'{symbol} {interval} Technical Analysis - {datetime.now().strftime("%Y-%m-%d %H:%M")}', 
                fontsize=16, fontweight='bold')
    
    plt.tight_layout()
    plt.subplots_adjust(top=0.93)
    
    return fig

def save_chart(fig, filename):
    """保存图表"""
    fig.savefig(filename, dpi=150, bbox_inches='tight', facecolor='white', 
                edgecolor='none', format='png')
    
    # 检查文件大小
    file_size = os.path.getsize(filename) / 1024  # KB
    print(f"✅ Chart saved: {filename} ({file_size:.1f} KB)")
    
    plt.close(fig)

def main():
    """主函数 - 测试图表生成"""
    print("Creating English Technical Chart...")
    
    # 创建测试数据
    dates = pd.date_range(start='2025-07-11', end='2025-07-16', freq='H')
    np.random.seed(42)
    
    test_data = {
        'close': 60000 + np.cumsum(np.random.randn(len(dates)) * 100),
        'RSI': 50 + np.random.randn(len(dates)) * 15,
        'MACD': np.random.randn(len(dates)) * 50,
        'MACD_signal': np.random.randn(len(dates)) * 40,
        'MACD_hist': np.random.randn(len(dates)) * 20,
        'SMA_20': 60000 + np.cumsum(np.random.randn(len(dates)) * 80),
        'SMA_50': 60000 + np.cumsum(np.random.randn(len(dates)) * 60),
        'EMA_12': 60000 + np.cumsum(np.random.randn(len(dates)) * 90),
        'EMA_26': 60000 + np.cumsum(np.random.randn(len(dates)) * 70),
        'key_support': 58000,
        'key_resistance': 62000
    }
    
    df = pd.DataFrame(test_data, index=dates)
    df['RSI'] = np.clip(df['RSI'], 0, 100)  # 限制RSI范围
    
    # 创建图表
    fig = create_technical_chart(df)
    
    # 保存图表
    save_chart(fig, 'english_technical_chart.png')
    
    print("✅ English chart created successfully!")
    print("No Chinese character warnings should appear.")

if __name__ == "__main__":
    main()
