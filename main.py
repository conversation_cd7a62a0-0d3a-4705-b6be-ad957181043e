# main.py
from data_fetcher import fetch_klines, fetch_realtime_klines, get_latest_price
from technical_analysis import calculate_indicators
from visualization import plot_technical_analysis, generate_analysis_report
import pandas as pd
from config import SYMBOL, INTERVAL
import datetime


def main():
    print(f"开始分析: {SYMBOL} {INTERVAL}")
    print(f"分析时间: {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

    # 显示当前最新价格
    try:
        current_price = get_latest_price()
        print(f"当前最新价格: {current_price:.2f}")
    except Exception as e:
        print(f"获取最新价格失败: {e}")

    # 1. 获取最新K线数据
    print("\n获取最新K线数据...")
    df = fetch_realtime_klines()  # 使用实时数据获取函数

    # 2. 计算技术指标
    print("计算技术指标和支撑/阻力位...")
    df = calculate_indicators(df)

    # 3. 保存完整数据（供DeepSeek分析）
    data_filename = f"{SYMBOL}_{INTERVAL}_data.csv"
    df.to_csv(data_filename, encoding='utf-8-sig')
    print(f"完整数据已保存到: {data_filename}")

    # 4. 生成分析报告
    print("生成分析报告...")
    generate_analysis_report(df, SYMBOL, INTERVAL)

    # 5. 可视化
    print("生成可视化图表...")
    plot_technical_analysis(df)

    print("\n分析完成！请将以下文件发送给DeepSeek进行深入分析:")
    print(f"1. {data_filename}")
    print(f"2. {SYMBOL}_{INTERVAL}_analysis.png")
    print(f"3. {SYMBOL}_{INTERVAL}_trend_volatility.png")
    print(f"4. analysis_report.md")


def quick_update():
    """快速更新 - 只获取最新数据和关键指标"""
    print(f"快速更新: {SYMBOL} {INTERVAL}")
    print(f"更新时间: {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

    try:
        # 获取最新价格
        current_price = get_latest_price()
        print(f"当前价格: {current_price:.2f}")

        # 获取最新K线数据
        df = fetch_realtime_klines()

        # 计算关键技术指标
        df = calculate_indicators(df)

        # 显示关键信息
        latest = df.iloc[-1]
        print(f"\n=== 关键指标 ===")
        print(f"RSI: {latest['RSI']:.2f}")
        print(f"MACD: {latest['MACD']:.4f}")
        print(f"支撑位: {latest['key_support']:.2f}")
        print(f"阻力位: {latest['key_resistance']:.2f}")
        print(f"交易建议: {latest['trade_suggestion']}")

        return df

    except Exception as e:
        print(f"快速更新失败: {e}")
        return None


if __name__ == "__main__":
    import sys

    # 检查命令行参数
    if len(sys.argv) > 1 and sys.argv[1] == "quick":
        quick_update()
    else:
        main()