# main.py
from data_fetcher import fetch_klines
from technical_analysis import calculate_indicators
from report_generator import generate_full_report, save_optimized_csv
from visualization import (plot_technical_analysis, generate_trend_volatility_chart,
                           generate_support_resistance_chart, generate_momentum_chart)
import os
from config import SYMBOL, INTERVAL, DAYS_TO_ANALYZE


def main():
    print(f"开始分析: {SYMBOL} {INTERVAL} (最近{DAYS_TO_ANALYZE}天)")
    print("=" * 50)

    # 1. 获取数据
    print(f"获取{DAYS_TO_ANALYZE}天的1小时K线数据...")
    try:
        df = fetch_klines()
        print(f"✅ 成功获取 {len(df)} 条K线数据")
        print(f"时间范围: {df.index[0].strftime('%Y-%m-%d')} 至 {df.index[-1].strftime('%Y-%m-%d')}")
    except Exception as e:
        print(f"❌ 数据获取失败: {str(e)}")
        return

    # 2. 计算技术指标
    print("\n计算技术指标和支撑/阻力位...")
    try:
        df = calculate_indicators(df)
        print("✅ 技术指标计算完成")
        print(f"- 包含指标: RSI, MACD, ADX, ATR, 移动平均线, 支撑/阻力位等")
    except Exception as e:
        print(f"❌ 技术指标计算失败: {str(e)}")
        return

    # 3. 创建输出目录
    output_dir = f"{SYMBOL}_{INTERVAL}_analysis"
    os.makedirs(output_dir, exist_ok=True)
    os.chdir(output_dir)
    print(f"\n分析结果将保存到: {os.getcwd()}")

    # 4. 生成报告和优化数据
    print("\n生成分析文件:")
    try:
        # 生成纯文本报告（包含关键事件、斐波那契水平和多时间框架分析）
        print("- 生成分析报告...")
        report_file = generate_full_report(df)
        print(f"  ✅ 报告已生成: {report_file}")

        # 生成优化后的CSV
        print("- 生成优化数据文件...")
        data_file = save_optimized_csv(df)
        print(f"  ✅ 数据文件已生成: {data_file}")

        # 生成各种图表
        print("\n生成技术分析图表:")
        print("- 综合技术分析图表（含关键事件和斐波那契水平）...")
        chart1 = plot_technical_analysis(df)
        print(f"  ✅ 图表已生成: {chart1}")

        print("- 趋势波动图表...")
        chart2 = generate_trend_volatility_chart(df)
        print(f"  ✅ 图表已生成: {chart2}")

        print("- 支撑阻力图表（含斐波那契水平）...")
        chart3 = generate_support_resistance_chart(df)
        print(f"  ✅ 图表已生成: {chart3}")

        print("- 动量指标图表（含动态止盈策略）...")
        chart4 = generate_momentum_chart(df)
        print(f"  ✅ 图表已生成: {chart4}")

        files = [report_file, data_file, chart1, chart2, chart3, chart4]
    except Exception as e:
        print(f"❌ 文件生成失败: {str(e)}")
        return

    # 5. 输出使用指南
    print("\n" + "=" * 50)
    print("✨ 分析完成！请将以下6个文件上传到DeepSeek:")
    print("=" * 50)
    for i, file in enumerate(files, 1):
        file_type = ""
        if file.endswith('.txt'):
            file_type = " (详细分析报告)"
        elif file.endswith('.csv'):
            file_type = " (优化数据文件)"
        elif 'analysis.png' in file:
            file_type = " (综合技术分析图 - 含关键事件)"
        elif 'trend_volatility.png' in file:
            file_type = " (趋势波动图)"
        elif 'support_resistance.png' in file:
            file_type = " (支撑阻力图 - 含斐波那契水平)"
        elif 'momentum.png' in file:
            file_type = " (动量指标图 - 含动态止盈策略)"
        print(f"{i}. {file}{file_type}")

    print("\n上传后，请使用以下提问内容:")
    print("=" * 50)
    print("请基于提供的技术分析数据，完成以下分析:")
    print("1. 关键支撑位/阻力位分析（包括斐波那契回撤水平）")
    print("2. 多时间框架趋势分析（日线/4小时/1小时）")
    print("3. 当前交易信号评估（基于RSI、MACD等指标）")
    print("4. 具体的交易策略建议（入场点、止损位、目标位）")
    print("5. 风险管理方案（包括动态止盈策略）")
    print("6. 关键事件对市场的影响评估")
    print("=" * 50)
    print("提示：分析报告中已包含详细分析，可作为参考")
    print("=" * 50)


if __name__ == "__main__":
    main()