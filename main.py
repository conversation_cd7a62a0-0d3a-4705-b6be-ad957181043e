# main.py
from data_fetcher import fetch_klines
from technical_analysis import calculate_indicators
from report_generator import generate_full_report, save_optimized_csv
import os
from config import SYMBOL, INTERVAL, DAYS_TO_ANALYZE

def main():
    print(f"开始分析: {SYMBOL} {INTERVAL} (最近{DAYS_TO_ANALYZE}天)")
    
    # 1. 获取数据
    print(f"获取{DAYS_TO_ANALYZE}天的1小时K线数据...")
    try:
        df = fetch_klines()
        print(f"成功获取 {len(df)} 条K线数据")
    except Exception as e:
        print(f"数据获取失败: {str(e)}")
        return
    
    # 2. 计算技术指标
    print("计算技术指标和支撑/阻力位...")
    try:
        df = calculate_indicators(df)
        print("技术指标计算完成")
    except Exception as e:
        print(f"技术指标计算失败: {str(e)}")
        return
    
    # 3. 创建输出目录
    output_dir = f"{SYMBOL}_{INTERVAL}_analysis"
    os.makedirs(output_dir, exist_ok=True)
    os.chdir(output_dir)
    print(f"分析结果将保存到: {os.getcwd()}")
    
    # 4. 生成报告和优化数据
    print("\n生成分析文件:")
    try:
        # 生成纯文本报告
        report_file = generate_full_report(df)
        
        # 生成优化后的CSV
        data_file = save_optimized_csv(df)
        
        files = [report_file, data_file]
    except Exception as e:
        print(f"文件生成失败: {str(e)}")
        return
    
    # 5. 输出使用指南
    print("\n" + "="*50)
    print("分析完成！请将以下2个文件上传到DeepSeek:")
    for file in files:
        print(f"- {file}")
    
    print("\n上传后，请使用以下提问内容:")
    print("="*50)
    print("请分析交易数据，回答以下问题:")
    print("1. 当前价格附近的关键支撑位和阻力位是什么？")
    print("2. 基于技术指标，当前市场趋势如何？")
    print("3. 给出具体的做多/做空建议，包括入场点、止损位和目标位")
    print("4. 评估风险/回报比")
    print("5. 提供风险管理建议")
    print("="*50)
    print("提示：报告文件中已包含详细分析，可作为参考")

if __name__ == "__main__":
    main()