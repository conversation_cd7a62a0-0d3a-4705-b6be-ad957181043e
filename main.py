# main.py
from data_fetcher import fetch_klines
from technical_analysis import calculate_indicators
from visualization import plot_technical_analysis, generate_analysis_report
import pandas as pd
from config import SYMBOL, INTERVAL


def main():
    print(f"开始分析: {SYMBOL} {INTERVAL}")

    # 1. 获取数据
    print("获取K线数据...")
    df = fetch_klines()

    # 2. 计算技术指标
    print("计算技术指标和支撑/阻力位...")
    df = calculate_indicators(df)

    # 3. 保存完整数据（供DeepSeek分析）
    data_filename = f"{SYMBOL}_{INTERVAL}_data.csv"
    df.to_csv(data_filename, encoding='utf-8-sig')
    print(f"完整数据已保存到: {data_filename}")

    # 4. 生成分析报告
    print("生成分析报告...")
    generate_analysis_report(df, SYMBOL, INTERVAL)

    # 5. 可视化
    print("生成可视化图表...")
    plot_technical_analysis(df)

    print("\n分析完成！请将以下文件发送给DeepSeek进行深入分析:")
    print(f"1. {data_filename}")
    print(f"2. {SYMBOL}_{INTERVAL}_analysis.png")
    print(f"3. {SYMBOL}_{INTERVAL}_trend_volatility.png")
    print(f"4. analysis_report.md")


if __name__ == "__main__":
    main()