# main.py
from data_fetcher import fetch_klines
from technical_analysis import calculate_indicators
from visualization import (save_main_data_file, 
                         generate_analysis_summary, 
                         generate_technical_chart, 
                         generate_analysis_guide)
import os
from config import SYMBOL, INTERVAL, DAYS_TO_ANALYZE

def main():
    print(f"开始分析: {SYMBOL} {INTERVAL} (最近{DAYS_TO_ANALYZE}天)")
    
    # 1. 获取数据
    print(f"获取{DAYS_TO_ANALYZE}天的1小时K线数据...")
    try:
        df = fetch_klines()
        print(f"成功获取 {len(df)} 条K线数据")
    except Exception as e:
        print(f"数据获取失败: {str(e)}")
        return
    
    # 2. 计算技术指标
    print("计算技术指标和支撑/阻力位...")
    try:
        df = calculate_indicators(df)
        print("技术指标计算完成")
    except Exception as e:
        print(f"技术指标计算失败: {str(e)}")
        return
    
    # 3. 创建输出目录
    output_dir = f"{SYMBOL}_{INTERVAL}_analysis"
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)
        print(f"创建分析目录: {output_dir}")
    else:
        print(f"使用现有分析目录: {output_dir}")

    # 保存当前目录，稍后切换回来
    original_dir = os.getcwd()
    os.chdir(output_dir)
    print(f"分析结果将保存到: {os.path.abspath('.')}")
    
    # 4. 生成四个核心文件
    print("\n生成分析文件:")
    files = []

    try:
        print("1/4 保存主数据文件...")
        data_file = save_main_data_file(df)
        files.append(data_file)

        print("2/4 生成分析摘要...")
        summary_file = generate_analysis_summary(df)
        files.append(summary_file)

        print("3/4 生成技术图表...")
        chart_file = generate_technical_chart(df)
        files.append(chart_file)

        print("4/4 生成提问指南...")
        guide_file = generate_analysis_guide()
        files.append(guide_file)

        print("✅ 所有文件生成完成！")

    except Exception as e:
        print(f"❌ 文件生成失败: {str(e)}")
        # 切换回原始目录
        os.chdir(original_dir)
        return
    
    # 5. 输出使用指南
    print("\n" + "="*50)
    print("分析完成！请将以下4个文件上传到DeepSeek:")
    for file in files:
        print(f"- {file}")
    
    print("\n上传后，请复制以下提问内容:")
    print("="*50)
    try:
        with open(guide_file, "r", encoding="utf-8") as f:
            print(f.read())
    except:
        print("无法读取提问指南文件，请手动打开查看内容")
    
    print("="*50)
    print("提示：在DeepSeek中上传文件后，将上述内容粘贴到提问框中")

    # 切换回原始目录
    os.chdir(original_dir)
    print(f"\n已切换回原始目录: {os.getcwd()}")

if __name__ == "__main__":
    main()