# report_generator.py
import pandas as pd
import numpy as np
from datetime import datetime
from config import SYMBOL, INTERVAL, DAYS_TO_ANALYZE


def generate_full_report(df):
    """生成完整分析报告（纯文本）"""
    # 只保留最近5天的数据
    analysis_df = df.iloc[-DAYS_TO_ANALYZE * 24:]
    last_row = analysis_df.iloc[-1]

    # 计算关键指标
    last_24h_change = analysis_df['pct_change'][-24:].sum() if len(analysis_df) >= 24 else np.nan
    price_trend = "上涨" if last_24h_change > 0 else "下跌" if not np.isnan(last_24h_change) else "盘整"

    # 生成报告内容
    report = f"""
# {SYMBOL} {INTERVAL} 技术分析报告 ({DAYS_TO_ANALYZE}天数据)

**报告生成时间**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

## 数据概览
- **总数据点**: {len(analysis_df)} 条
- **时间范围**: {analysis_df.index[0].strftime('%Y-%m-%d %H:%M')} 至 {analysis_df.index[-1].strftime('%Y-%m-%d %H:%M')}
- **当前价格**: {last_row['close']}
- **24小时变化**: {last_24h_change:.2f}% ({price_trend})
- **5天最高价**: {analysis_df['high'].max():.4f}
- **5天最低价**: {analysis_df['low'].min():.4f}

## 关键指标分析
### 趋势指标
- **RSI (14)**: {last_row.get('RSI', 'N/A'):.2f} - {'超买' if last_row.get('RSI', 0) > 70 else '超卖' if last_row.get('RSI', 0) < 30 else '中性'}
- **MACD**: {last_row.get('MACD', 'N/A'):.4f} | 信号线: {last_row.get('MACD_signal', 'N/A'):.4f} - {'看涨' if last_row.get('MACD', 0) > last_row.get('MACD_signal', 0) else '看跌'}
- **ADX (趋势强度)**: {last_row.get('ADX', 'N/A'):.2f} - {'强趋势' if last_row.get('ADX', 0) > 25 else '弱趋势'}
- **ATR (波动率)**: {last_row.get('ATR', 'N/A'):.4f} - 当前市场波动率

### 移动平均线
- **20周期SMA**: {last_row.get('SMA_20', 'N/A'):.4f}
- **50周期SMA**: {last_row.get('SMA_50', 'N/A'):.4f}
- **200周期SMA**: {last_row.get('SMA_200', 'N/A'):.4f}
- **12周期EMA**: {last_row.get('EMA_12', 'N/A'):.4f}
- **26周期EMA**: {last_row.get('EMA_26', 'N/A'):.4f}

## 支撑位与阻力位分析
### 主要支撑位
1. **关键支撑位**: {last_row.get('key_support', 'N/A'):.4f} (综合水平)
2. **枢轴支撑位1 (S1)**: {last_row.get('s1', 'N/A'):.4f}
3. **枢轴支撑位2 (S2)**: {last_row.get('s2', 'N/A'):.4f}
4. **近期低点**: {last_row.get('recent_low', 'N/A'):.4f}

### 主要阻力位
1. **关键阻力位**: {last_row.get('key_resistance', 'N/A'):.4f} (综合水平)
2. **枢轴阻力位1 (R1)**: {last_row.get('r1', 'N/A'):.4f}
3. **枢轴阻力位2 (R2)**: {last_row.get('r2', 'N/A'):.4f}
4. **近期高点**: {last_row.get('recent_high', 'N/A'):.4f}

## 交易建议
### 当前信号
- **建议方向**: {last_row.get('trade_suggestion', '暂无建议')}
- **价格位置**: 处于支撑位上方 {((last_row['close'] - last_row['key_support']) / last_row['key_support'] * 100):.2f}%

### 具体操作建议
1. **做多策略**:
   - 入场点: {last_row.get('key_support', 'N/A'):.4f} 附近
   - 止损: {last_row.get('key_support', 0) * 0.995:.4f} (低于支撑位0.5%)
   - 目标: {last_row.get('key_resistance', 'N/A'):.4f}
   - 风险回报比: 1:{(last_row.get('key_resistance', 0) - last_row.get('key_support', 0)) / (last_row.get('key_support', 0) * 0.005):.1f}

2. **做空策略**:
   - 入场点: {last_row.get('key_resistance', 'N/A'):.4f} 附近
   - 止损: {last_row.get('key_resistance', 0) * 1.005:.4f} (高于阻力位0.5%)
   - 目标: {last_row.get('key_support', 'N/A'):.4f}
   - 风险回报比: 1:{(last_row.get('key_resistance', 0) - last_row.get('key_support', 0)) / (last_row.get('key_resistance', 0) * 0.005):.1f}

## 风险管理
- **最大风险**: 单笔交易不超过总资金的2%
- **止损策略**: 基于ATR设置止损，当前ATR为{last_row.get('ATR', 'N/A'):.4f}，建议止损距离: {last_row.get('ATR', 0) * 1.5:.4f}
- **仓位计算**: 风险金额 / (入场价 - 止损价)

## 数据摘要
{generate_data_summary(analysis_df)}

## 完整数据
完整数据集请查看附件CSV文件，包含以下指标:
- 开盘价, 最高价, 最低价, 收盘价, 成交量
- RSI, MACD, ADX, ATR
- 移动平均线(SMA, EMA)
- 支撑位/阻力位
- 交易建议
"""

    # 保存报告
    filename = f"{SYMBOL}_{INTERVAL}_analysis_report.txt"
    with open(filename, "w", encoding="utf-8") as f:
        f.write(report)

    print(f"分析报告已生成: {filename}")
    return filename


def generate_data_summary(df):
    """生成数据摘要（文本表格）"""
    # 只显示最新5条和最早5条数据
    summary_df = pd.concat([df.head(5), df.tail(5)])

    # 创建文本表格
    table = "时间 | 开盘价 | 最高价 | 最低价 | 收盘价 | 成交量 | RSI | MACD | 支撑位 | 阻力位\n"
    table += "---|---|---|---|---|---|---|---|---|---\n"

    for index, row in summary_df.iterrows():
        table += f"{index.strftime('%m-%d %H:%M')} | {row['open']:.4f} | {row['high']:.4f} | {row['low']:.4f} | {row['close']:.4f} | "
        table += f"{row['volume']:.2f} | {row.get('RSI', 'N/A'):.2f} | {row.get('MACD', 'N/A'):.4f} | "
        table += f"{row.get('key_support', 'N/A'):.4f} | {row.get('key_resistance', 'N/A'):.4f}\n"

    return table


def save_optimized_csv(df):
    """保存优化后的CSV文件"""
    filename = f"{SYMBOL}_{INTERVAL}_{DAYS_TO_ANALYZE}days_data.csv"

    # 只保留最近5天的数据
    df = df.iloc[-DAYS_TO_ANALYZE * 24:]

    # 重命名列名，使其更易理解
    df = df.rename(columns={
        'open': '开盘价',
        'high': '最高价',
        'low': '最低价',
        'close': '收盘价',
        'volume': '成交量',
        'pct_change': '价格变化%',
        'RSI': '相对强弱指数',
        'MACD': 'MACD线',
        'MACD_signal': '信号线',
        'ADX': '平均趋向指数',
        'ATR': '平均真实波幅',
        'key_support': '关键支撑位',
        'key_resistance': '关键阻力位',
        'trade_suggestion': '交易建议'
    })

    # 只保留关键列
    keep_columns = [
        '开盘价', '最高价', '最低价', '收盘价', '成交量', '价格变化%',
        '相对强弱指数', 'MACD线', '信号线', '平均趋向指数', '平均真实波幅',
        '关键支撑位', '关键阻力位', '交易建议'
    ]
    df = df[[col for col in keep_columns if col in df.columns]]

    # 保存文件
    df.to_csv(filename, encoding='utf-8-sig')
    print(f"优化后的数据文件已生成: {filename}")
    return filename