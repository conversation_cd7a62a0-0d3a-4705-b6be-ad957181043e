# report_generator.py
import pandas as pd
import numpy as np
from datetime import datetime
from config import SYMBOL, INTERVAL, DAYS_TO_ANALYZE


def generate_full_report(df):
    """生成完整分析报告（纯文本）"""
    # 只保留最近5天的数据
    analysis_df = df.iloc[-DAYS_TO_ANALYZE * 24:]
    last_row = analysis_df.iloc[-1]

    # 计算关键指标
    last_24h_change = analysis_df['pct_change'][-24:].sum() if len(analysis_df) >= 24 else np.nan
    price_trend = "上涨" if last_24h_change > 0 else "下跌" if not np.isnan(last_24h_change) else "盘整"

    # 生成报告内容
    report = f"""
# {SYMBOL} {INTERVAL} 技术分析报告 ({DAYS_TO_ANALYZE}天数据)

**报告生成时间**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

## 数据概览
- **总数据点**: {len(analysis_df)} 条
- **时间范围**: {analysis_df.index[0].strftime('%Y-%m-%d %H:%M')} 至 {analysis_df.index[-1].strftime('%Y-%m-%d %H:%M')}
- **当前价格**: {last_row['close']}
- **24小时变化**: {last_24h_change:.2f}% ({price_trend})
- **5天最高价**: {analysis_df['high'].max():.4f}
- **5天最低价**: {analysis_df['low'].min():.4f}

## 关键指标分析
### 趋势指标
- **RSI (14)**: {last_row.get('RSI', 'N/A'):.2f} - {'超买' if last_row.get('RSI', 0) > 70 else '超卖' if last_row.get('RSI', 0) < 30 else '中性'}
- **MACD**: {last_row.get('MACD', 'N/A'):.4f} | 信号线: {last_row.get('MACD_signal', 'N/A'):.4f} - {'看涨' if last_row.get('MACD', 0) > last_row.get('MACD_signal', 0) else '看跌'}
- **ADX (趋势强度)**: {last_row.get('ADX', 'N/A'):.2f} - {'强趋势' if last_row.get('ADX', 0) > 25 else '弱趋势'}
- **ATR (波动率)**: {last_row.get('ATR', 'N/A'):.4f} - 当前市场波动率

### 移动平均线
- **20周期SMA**: {last_row.get('SMA_20', 'N/A'):.4f}
- **50周期SMA**: {last_row.get('SMA_50', 'N/A'):.4f}
- **200周期SMA**: {last_row.get('SMA_200', 'N/A'):.4f}
- **12周期EMA**: {last_row.get('EMA_12', 'N/A'):.4f}
- **26周期EMA**: {last_row.get('EMA_26', 'N/A'):.4f}

## 支撑位与阻力位分析
### 主要支撑位
1. **关键支撑位**: {last_row.get('key_support', 'N/A'):.4f} (综合水平)
2. **枢轴支撑位1 (S1)**: {last_row.get('s1', 'N/A'):.4f}
3. **枢轴支撑位2 (S2)**: {last_row.get('s2', 'N/A'):.4f}
4. **近期低点**: {last_row.get('recent_low', 'N/A'):.4f}

### 主要阻力位
1. **关键阻力位**: {last_row.get('key_resistance', 'N/A'):.4f} (综合水平)
2. **枢轴阻力位1 (R1)**: {last_row.get('r1', 'N/A'):.4f}
3. **枢轴阻力位2 (R2)**: {last_row.get('r2', 'N/A'):.4f}
4. **近期高点**: {last_row.get('recent_high', 'N/A'):.4f}

## 交易建议
### 当前信号
- **建议方向**: {last_row.get('trade_suggestion', '观望')}
- **价格位置**: 处于支撑位上方 {((last_row['close'] - last_row['key_support']) / last_row['key_support'] * 100):.2f}%

### 具体操作建议
1. **做多策略**:
   - 入场点: {last_row.get('key_support', 'N/A'):.4f} 附近
   - 止损: {last_row.get('key_support', 0) * 0.995:.4f} (低于支撑位0.5%)
   - 目标: {last_row.get('key_resistance', 'N/A'):.4f}
   - 风险回报比: 1:{(last_row.get('key_resistance', 0) - last_row.get('key_support', 0)) / (last_row.get('key_support', 0) * 0.005):.1f}

2. **做空策略**:
   - 入场点: {last_row.get('key_resistance', 'N/A'):.4f} 附近
   - 止损: {last_row.get('key_resistance', 0) * 1.005:.4f} (高于阻力位0.5%)
   - 目标: {last_row.get('key_support', 'N/A'):.4f}
   - 风险回报比: 1:{(last_row.get('key_resistance', 0) - last_row.get('key_support', 0)) / (last_row.get('key_resistance', 0) * 0.005):.1f}

## 风险管理
- **最大风险**: 单笔交易不超过总资金的2%
- **止损策略**: 基于ATR设置止损，当前ATR为{last_row.get('ATR', 'N/A'):.4f}，建议止损距离: {last_row.get('ATR', 0) * 1.5:.4f}
- **仓位计算**: 风险金额 / (入场价 - 止损价)

## 数据摘要
{generate_data_summary(analysis_df)}

## 完整数据
完整数据集请查看附件CSV文件，包含以下指标:
- 开盘价, 最高价, 最低价, 收盘价, 成交量
- RSI, MACD, ADX, ATR
- 移动平均线(SMA, EMA)
- 支撑位/阻力位
- 交易建议
"""

    # 保存报告
    filename = f"{SYMBOL}_{INTERVAL}_analysis_report.txt"
    with open(filename, "w", encoding="utf-8") as f:
        f.write(report)

    print(f"分析报告已生成: {filename}")
    return filename


def generate_data_summary(df):
    """生成清晰的数据摘要表格"""
    # 显示最新10条数据，更有参考价值
    summary_df = df.tail(10)

    # 创建清晰的文本表格
    table = "\n### 最近10小时数据摘要\n\n"
    table += "| 月日 | 时分 | 收盘价 | 价格变化% | RSI | MACD | 支撑位 | 阻力位 | 交易信号 |\n"
    table += "|------|------|--------|----------|-----|------|--------|--------|----------|\n"

    for index, row in summary_df.iterrows():
        # 格式化时间为月日和时分
        date_str = index.strftime('%m-%d')
        time_str = index.strftime('%H:%M')

        # 格式化价格变化，添加方向指示
        pct_change = row.get('pct_change', 0)
        pct_str = f"{pct_change:+.2f}%" if not pd.isna(pct_change) else "0.00%"

        # 格式化RSI，添加状态指示
        rsi = row.get('RSI', 0)
        if pd.isna(rsi):
            rsi_str = "N/A"
        elif rsi > 70:
            rsi_str = f"{rsi:.1f}(超买)"
        elif rsi < 30:
            rsi_str = f"{rsi:.1f}(超卖)"
        else:
            rsi_str = f"{rsi:.1f}(正常)"

        # 格式化MACD
        macd = row.get('MACD', 0)
        macd_str = f"{macd:.3f}" if not pd.isna(macd) else "N/A"

        # 格式化支撑阻力位
        support = row.get('key_support', 0)
        resistance = row.get('key_resistance', 0)
        support_str = f"{support:.2f}" if not pd.isna(support) else "N/A"
        resistance_str = f"{resistance:.2f}" if not pd.isna(resistance) else "N/A"

        # 交易信号
        signal = row.get('trade_suggestion', '观望')

        table += f"| {date_str} | {time_str} | {row['close']:.2f} | {pct_str} | {rsi_str} | {macd_str} | {support_str} | {resistance_str} | {signal} |\n"

    # 添加统计信息
    table += f"\n### 统计信息\n"
    table += f"- **最高价**: {summary_df['high'].max():.2f}\n"
    table += f"- **最低价**: {summary_df['low'].min():.2f}\n"
    table += f"- **平均成交量**: {summary_df['volume'].mean():.0f}\n"
    table += f"- **价格波动范围**: {(summary_df['high'].max() - summary_df['low'].min()):.2f} ({((summary_df['high'].max() - summary_df['low'].min()) / summary_df['close'].mean() * 100):.2f}%)\n"

    return table


def save_optimized_csv(df):
    """保存优化后的CSV文件 - 内容清晰易读"""
    filename = f"{SYMBOL}_{INTERVAL}_{DAYS_TO_ANALYZE}days_data.csv"

    # 创建数据副本并只保留最近5天的数据
    df_clean = df.iloc[-DAYS_TO_ANALYZE * 24:].copy()

    # 添加时间信息列
    df_clean.insert(0, '月日', df_clean.index.strftime('%m-%d'))
    df_clean.insert(1, '时分', df_clean.index.strftime('%H:%M'))
    df_clean.insert(2, '小时', df_clean.index.hour)
    df_clean.insert(3, '星期', df_clean.index.strftime('%A'))

    # 重命名列名为中英文对照，便于理解
    column_mapping = {
        'open': 'Open_开盘价',
        'high': 'High_最高价',
        'low': 'Low_最低价',
        'close': 'Close_收盘价',
        'volume': 'Volume_成交量',
        'pct_change': 'PriceChange_价格变化率%',
        'RSI': 'RSI_相对强弱指数',
        'MACD': 'MACD_指标线',
        'MACD_signal': 'MACD_Signal_信号线',
        'MACD_hist': 'MACD_Hist_柱状图',
        'SMA_20': 'SMA20_简单移动平均',
        'SMA_50': 'SMA50_简单移动平均',
        'SMA_200': 'SMA200_简单移动平均',
        'EMA_12': 'EMA12_指数移动平均',
        'EMA_26': 'EMA26_指数移动平均',
        'BB_upper': 'BB_Upper_布林带上轨',
        'BB_middle': 'BB_Middle_布林带中轨',
        'BB_lower': 'BB_Lower_布林带下轨',
        'ADX': 'ADX_平均趋向指数',
        'ATR': 'ATR_平均真实波幅',
        'key_support': 'Support_关键支撑位',
        'key_resistance': 'Resistance_关键阻力位',
        'trade_suggestion': '交易信号'
    }

    # 重命名存在的列
    existing_columns = {k: v for k, v in column_mapping.items() if k in df_clean.columns}
    df_clean = df_clean.rename(columns=existing_columns)

    # 格式化数值，提高可读性
    for col in df_clean.columns:
        if df_clean[col].dtype in ['float64', 'float32']:
            if any(keyword in col for keyword in ['价格', 'Price', 'Open', 'High', 'Low', 'Close', 'Support', 'Resistance']):
                df_clean[col] = df_clean[col].round(2)  # 价格保留2位小数
            elif 'Volume' in col or '成交量' in col:
                df_clean[col] = df_clean[col].round(0).astype('int64')  # 成交量取整
            elif '变化率' in col or 'Change' in col:
                df_clean[col] = df_clean[col].round(3)  # 变化率保留3位小数
            elif 'RSI' in col or 'ADX' in col:
                df_clean[col] = df_clean[col].round(1)  # 指数保留1位小数
            else:
                df_clean[col] = df_clean[col].round(4)  # 其他指标保留4位小数

    # 选择最重要的列，确保CSV文件清晰
    priority_columns = [
        '月日', '时分', '小时', '星期',
        'Open_开盘价', 'High_最高价', 'Low_最低价', 'Close_收盘价', 'Volume_成交量',
        'PriceChange_价格变化率%', 'RSI_相对强弱指数', 'MACD_指标线', 'MACD_Signal_信号线',
        'SMA20_简单移动平均', 'SMA50_简单移动平均', 'EMA12_指数移动平均', 'EMA26_指数移动平均',
        'ADX_平均趋向指数', 'ATR_平均真实波幅',
        'Support_关键支撑位', 'Resistance_关键阻力位', '交易信号'
    ]

    # 只保留存在的优先列
    final_columns = [col for col in priority_columns if col in df_clean.columns]
    df_final = df_clean[final_columns]

    # 添加数据说明行（作为注释）
    with open(filename, 'w', encoding='utf-8-sig', newline='') as f:
        # 写入文件头说明
        f.write(f"# {SYMBOL} {INTERVAL} 技术分析数据\n")
        f.write(f"# 数据时间范围: {df_final['月日'].iloc[0]} {df_final['时分'].iloc[0]} 到 {df_final['月日'].iloc[-1]} {df_final['时分'].iloc[-1]}\n")
        f.write(f"# 数据行数: {len(df_final)} 条\n")
        f.write(f"# 生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
        f.write("# 列说明: 月日(MM-DD) 时分(HH:MM) 星期(English) 交易信号(中文) 中英文对照列名便于理解\n")
        f.write("#" + "="*80 + "\n")

        # 写入实际数据
        df_final.to_csv(f, index=False, float_format='%.4f')

    # 显示文件信息
    import os
    file_size = os.path.getsize(filename) / 1024  # KB
    print(f"✅ 清晰格式的数据文件已生成: {filename}")
    print(f"   📊 数据行数: {len(df_final)} 条")
    print(f"   📋 数据列数: {len(df_final.columns)} 列")
    print(f"   💾 文件大小: {file_size:.1f} KB")
    print(f"   ⏰ 时间范围: {df_final['月日'].iloc[0]} {df_final['时分'].iloc[0]} 到 {df_final['月日'].iloc[-1]} {df_final['时分'].iloc[-1]}")
    print(f"   🎯 包含指标: 价格数据、技术指标、支撑阻力位、交易信号")
    print(f"   📅 时间格式: 月日(MM-DD) + 时分(HH:MM)")

    return filename