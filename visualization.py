# visualization.py
import warnings
warnings.filterwarnings('ignore', category=UserWarning, module='matplotlib')

import matplotlib.pyplot as plt
from matplotlib.gridspec import GridSpec
import pandas as pd
import numpy as np
import os
from datetime import datetime
from config import SYMBOL, INTERVAL, DAYS_TO_ANALYZE, PLOT_LAST_POINTS

# 设置英文字体，避免中文字符问题
plt.rcParams['font.family'] = ['DejaVu Sans', 'Arial', 'sans-serif']
plt.rcParams['font.sans-serif'] = ['DejaVu Sans', 'Arial']
plt.rcParams['axes.unicode_minus'] = False
plt.rcParams['font.size'] = 10
plt.rcParams['axes.titlesize'] = 12
plt.rcParams['axes.labelsize'] = 10
plt.rcParams['legend.fontsize'] = 9

def setup_english_fonts():
    """设置英文字体并抑制中文字符警告"""
    plt.rcParams['font.family'] = ['DejaVu Sans', 'Arial', 'sans-serif']
    plt.rcParams['font.size'] = 10

    # 抑制中文字符缺失警告
    import warnings
    warnings.filterwarnings('ignore', category=UserWarning, module='matplotlib')

    print("✅ 英文字体设置完成，中文字符警告已抑制")

def save_main_data_file(df):
    """保存主数据文件 - 优化格式便于AI分析"""
    filename = f"{SYMBOL}_{INTERVAL}_{DAYS_TO_ANALYZE}days_data.csv"

    # 创建优化的数据副本
    df_export = df.copy()

    # 重命名列为英文，便于DeepSeek理解
    column_mapping = {
        'open': 'Open_Price',
        'high': 'High_Price',
        'low': 'Low_Price',
        'close': 'Close_Price',
        'volume': 'Volume',
        'pct_change': 'Price_Change_Pct',
        'RSI': 'RSI_14',
        'MACD': 'MACD_Line',
        'MACD_signal': 'MACD_Signal',
        'MACD_hist': 'MACD_Histogram',
        'SMA_20': 'SMA_20_Period',
        'SMA_50': 'SMA_50_Period',
        'SMA_200': 'SMA_200_Period',
        'EMA_12': 'EMA_12_Period',
        'EMA_26': 'EMA_26_Period',
        'BB_upper': 'Bollinger_Upper',
        'BB_middle': 'Bollinger_Middle',
        'BB_lower': 'Bollinger_Lower',
        'ATR': 'ATR_14',
        'ADX': 'ADX_14',
        'key_support': 'Key_Support_Level',
        'key_resistance': 'Key_Resistance_Level',
        'trade_suggestion': 'Trade_Suggestion'
    }

    # 只保留存在的列并重命名
    existing_columns = {k: v for k, v in column_mapping.items() if k in df_export.columns}
    df_export = df_export[list(existing_columns.keys())].rename(columns=existing_columns)

    # 格式化数值，保留合适的小数位数
    for col in df_export.columns:
        if df_export[col].dtype in ['float64', 'float32']:
            if 'Price' in col or 'Level' in col:
                df_export[col] = df_export[col].round(2)  # 价格保留2位小数
            elif 'Volume' in col:
                df_export[col] = df_export[col].round(0).astype('int64')  # 成交量取整
            elif 'Pct' in col:
                df_export[col] = df_export[col].round(3)  # 百分比保留3位小数
            else:
                df_export[col] = df_export[col].round(4)  # 其他指标保留4位小数

    # 添加时间列（便于分析）
    df_export.insert(0, 'DateTime', df_export.index.strftime('%Y-%m-%d %H:%M:%S'))
    df_export.insert(1, 'Date', df_export.index.strftime('%Y-%m-%d'))
    df_export.insert(2, 'Hour', df_export.index.hour)

    # 限制数据量，只保留最近的数据（避免文件过大）
    max_rows = 168  # 7天 * 24小时 = 168行，适中的数据量
    if len(df_export) > max_rows:
        df_export = df_export.tail(max_rows)
        print(f"⚠️ 数据量较大，只保留最近{max_rows}条记录（约7天数据）")

    # 保存为标准CSV格式
    df_export.to_csv(filename, index=False, encoding='utf-8-sig',
                     float_format='%.4f', date_format='%Y-%m-%d %H:%M:%S')

    # 显示文件信息
    file_size = os.path.getsize(filename) / 1024  # KB
    print(f"✅ 主数据文件已生成: {filename}")
    print(f"   - 数据行数: {len(df_export)}")
    print(f"   - 数据列数: {len(df_export.columns)}")
    print(f"   - 文件大小: {file_size:.1f} KB")
    print(f"   - 时间范围: {df_export['DateTime'].iloc[0]} 到 {df_export['DateTime'].iloc[-1]}")

    return filename

def generate_analysis_summary(df):
    """生成分析摘要文件"""
    filename = f"{SYMBOL}_{INTERVAL}_analysis_summary.md"
    last_row = df.iloc[-1]
    
    # 计算24小时变化率
    last_24h_change = df['pct_change'][-24:].sum() if len(df) >= 24 else np.nan
    
    content = f"""
# {SYMBOL} {INTERVAL} 技术分析摘要 ({DAYS_TO_ANALYZE}天数据)

**报告生成时间**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

## 关键指标
| 指标 | 值 | 状态 |
|------|----|------|
| 当前价格 | {last_row['close']} | - |
| 24小时变化 | {last_24h_change:.2f}% | {'↑' if last_24h_change > 0 else '↓' if not np.isnan(last_24h_change) else 'N/A'} |
| RSI (14) | {last_row.get('RSI', 'N/A'):.2f} | {'超买' if last_row.get('RSI', 0) > 70 else '超卖' if last_row.get('RSI', 0) < 30 else '正常'} |
| MACD | {last_row.get('MACD', 'N/A'):.4f} | {'看涨' if last_row.get('MACD', 0) > last_row.get('MACD_signal', 0) else '看跌'} |
| ADX | {last_row.get('ADX', 'N/A'):.2f} | {'强趋势' if last_row.get('ADX', 0) > 25 else '弱趋势'} |
| ATR | {last_row.get('ATR', 'N/A'):.4f} | 波动率 |

## 支撑位与阻力位
### 关键支撑位
1. {last_row.get('key_support', 'N/A'):.4f} (主要支撑)
2. {last_row.get('s1', 'N/A'):.4f} (次要支撑)
3. {last_row.get('s2', 'N/A'):.4f} (强支撑)

### 关键阻力位
1. {last_row.get('key_resistance', 'N/A'):.4f} (主要阻力)
2. {last_row.get('r1', 'N/A'):.4f} (次要阻力)
3. {last_row.get('r2', 'N/A'):.4f} (强阻力)

## 交易建议
{last_row.get('trade_suggestion', '暂无建议')}

> 完整数据请查看附件CSV文件
"""

    with open(filename, "w", encoding="utf-8") as f:
        f.write(content)
    
    print(f"分析摘要已生成: {filename}")
    return filename

def plot_technical_analysis(df):
    """生成完整的技术分析图表"""
    filename = f"{SYMBOL}_{INTERVAL}_analysis.png"

    # 设置英文字体
    setup_english_fonts()

    # 只显示最新的数据点
    plot_df = df.iloc[-PLOT_LAST_POINTS:]
    last_row = plot_df.iloc[-1]

    # 创建图表布局
    plt.figure(figsize=(16, 14), dpi=100)
    plt.suptitle(f'{SYMBOL} Technical Analysis - {INTERVAL} Chart', fontsize=18, fontweight='bold')

    gs = GridSpec(5, 1, height_ratios=[3, 1, 1, 1, 1])

    # 1. 价格和关键水平
    ax1 = plt.subplot(gs[0])
    ax1.plot(plot_df.index, plot_df['close'], label='Close Price', color='black', linewidth=1.5)

    # 绘制关键支撑位和阻力位
    if 'key_support' in last_row and not np.isnan(last_row['key_support']):
        ax1.axhline(y=last_row['key_support'], color='green', linestyle='--', alpha=0.7,
                    label=f'Key Support: {last_row["key_support"]:.2f}')
    if 'key_resistance' in last_row and not np.isnan(last_row['key_resistance']):
        ax1.axhline(y=last_row['key_resistance'], color='red', linestyle='--', alpha=0.7,
                    label=f'Key Resistance: {last_row["key_resistance"]:.2f}')

    # 绘制移动平均线
    ma_colors = {'SMA_20': 'blue', 'SMA_50': 'orange', 'SMA_200': 'purple', 'EMA_12': 'green', 'EMA_26': 'red'}
    for col, color in ma_colors.items():
        if col in plot_df.columns:
            period = col.split('_')[-1]
            line_style = '--' if 'SMA' in col else '-'
            ax1.plot(plot_df.index, plot_df[col], label=f'{col.replace("_", " ")}',
                    color=color, linestyle=line_style, alpha=0.8)

    # 绘制布林带
    if all(col in plot_df.columns for col in ['BB_upper', 'BB_lower']):
        ax1.fill_between(plot_df.index, plot_df['BB_lower'], plot_df['BB_upper'],
                         color='lightblue', alpha=0.3, label='Bollinger Bands')

    ax1.set_title(f'Price & Key Levels | Trade Signal: {last_row.get("trade_suggestion", "N/A")}', fontsize=14)
    ax1.set_ylabel('Price (USDT)')
    ax1.legend(loc='best', fontsize=9)
    ax1.grid(True, linestyle='--', alpha=0.7)

    # 2. RSI指标
    ax2 = plt.subplot(gs[1])
    if 'RSI' in plot_df.columns:
        ax2.plot(plot_df.index, plot_df['RSI'], label='RSI(14)', color='purple', linewidth=2)
        ax2.axhline(70, color='red', linestyle='--', label='Overbought (70)', alpha=0.8)
        ax2.axhline(30, color='green', linestyle='--', label='Oversold (30)', alpha=0.8)
        ax2.fill_between(plot_df.index, 30, 70, color='yellow', alpha=0.1, label='Normal Range')
        ax2.set_ylim(0, 100)
        ax2.set_ylabel('RSI')
        ax2.set_title('RSI Indicator', fontsize=12)
        ax2.legend(loc='best', fontsize=9)
        ax2.grid(True, linestyle='--', alpha=0.7)

        # 添加当前RSI值标注
        current_rsi = plot_df['RSI'].iloc[-1]
        ax2.text(plot_df.index[-1], current_rsi, f'{current_rsi:.1f}',
                fontsize=10, bbox=dict(boxstyle="round,pad=0.3", facecolor="lightblue", alpha=0.7))

    # 3. MACD指标
    ax3 = plt.subplot(gs[2])
    if all(col in plot_df.columns for col in ['MACD', 'MACD_signal']):
        ax3.plot(plot_df.index, plot_df['MACD'], label='MACD', color='blue', linewidth=2)
        ax3.plot(plot_df.index, plot_df['MACD_signal'], label='Signal Line', color='red', linewidth=2)

        # MACD柱状图
        if 'MACD_hist' in plot_df.columns:
            colors = np.where(plot_df['MACD_hist'] >= 0, 'green', 'red')
            ax3.bar(plot_df.index, plot_df['MACD_hist'], color=colors, alpha=0.6, label='MACD Histogram')

        ax3.axhline(0, color='gray', linestyle='-', alpha=0.5)
        ax3.set_ylabel('MACD')
        ax3.set_title('MACD Indicator', fontsize=12)
        ax3.legend(loc='best', fontsize=9)
        ax3.grid(True, linestyle='--', alpha=0.7)

        # 添加当前MACD值标注
        current_macd = plot_df['MACD'].iloc[-1]
        ax3.text(plot_df.index[-1], current_macd, f'{current_macd:.2f}',
                fontsize=10, bbox=dict(boxstyle="round,pad=0.3", facecolor="lightgreen", alpha=0.7))

    # 4. 成交量
    ax4 = plt.subplot(gs[3])
    if 'volume' in plot_df.columns:
        ax4.bar(plot_df.index, plot_df['volume'], color='lightblue', alpha=0.7, label='Volume')
        ax4.set_ylabel('Volume')
        ax4.set_title('Trading Volume', fontsize=12)
        ax4.legend(loc='best', fontsize=9)
        ax4.grid(True, linestyle='--', alpha=0.7)

    # 5. ATR和ADX
    ax5 = plt.subplot(gs[4])
    if 'ATR' in plot_df.columns:
        ax5.plot(plot_df.index, plot_df['ATR'], label='ATR(14)', color='orange', linewidth=2)
        ax5.set_ylabel('ATR', color='orange')
        ax5.tick_params(axis='y', labelcolor='orange')

    if 'ADX' in plot_df.columns:
        ax5_twin = ax5.twinx()
        ax5_twin.plot(plot_df.index, plot_df['ADX'], label='ADX(14)', color='purple', linewidth=2)
        ax5_twin.axhline(25, color='purple', linestyle='--', alpha=0.5, label='Strong Trend (25)')
        ax5_twin.set_ylabel('ADX', color='purple')
        ax5_twin.tick_params(axis='y', labelcolor='purple')
        ax5_twin.legend(loc='upper right', fontsize=9)

    ax5.set_title('Volatility (ATR) & Trend Strength (ADX)', fontsize=12)
    ax5.legend(loc='upper left', fontsize=9)
    ax5.grid(True, linestyle='--', alpha=0.7)

    plt.tight_layout()
    plt.savefig(filename, dpi=150, bbox_inches='tight')
    plt.close()

    print(f"✅ Technical analysis chart generated: {filename}")
    return filename


def generate_trend_volatility_chart(df):
    """生成趋势和波动性图表"""
    filename = f"{SYMBOL}_{INTERVAL}_trend_volatility.png"

    # 设置英文字体
    setup_english_fonts()

    # 只显示最新的数据点
    plot_df = df.iloc[-PLOT_LAST_POINTS:]

    # 创建图表
    fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(16, 12))

    # 1. 价格趋势和移动平均线
    ax1.plot(plot_df.index, plot_df['close'], label='Close Price', color='black', linewidth=2)

    # 移动平均线
    ma_colors = {'SMA_20': 'blue', 'SMA_50': 'orange', 'EMA_12': 'green', 'EMA_26': 'red'}
    for ma, color in ma_colors.items():
        if ma in plot_df.columns:
            ax1.plot(plot_df.index, plot_df[ma], label=ma, color=color, alpha=0.8)

    ax1.set_title('Price Trend & Moving Averages', fontsize=12, fontweight='bold')
    ax1.set_ylabel('Price (USDT)')
    ax1.legend(fontsize=9)
    ax1.grid(True, alpha=0.3)

    # 2. 波动率指标 (ATR)
    if 'ATR' in plot_df.columns:
        ax2.plot(plot_df.index, plot_df['ATR'], label='ATR(14)', color='orange', linewidth=2)
        ax2.fill_between(plot_df.index, 0, plot_df['ATR'], color='orange', alpha=0.3)
        ax2.set_title('Average True Range (Volatility)', fontsize=12, fontweight='bold')
        ax2.set_ylabel('ATR')
        ax2.legend(fontsize=9)
        ax2.grid(True, alpha=0.3)

    # 3. 趋势强度 (ADX)
    if 'ADX' in plot_df.columns:
        ax3.plot(plot_df.index, plot_df['ADX'], label='ADX(14)', color='purple', linewidth=2)
        ax3.axhline(25, color='red', linestyle='--', alpha=0.7, label='Strong Trend (25)')
        ax3.axhline(50, color='darkred', linestyle='--', alpha=0.7, label='Very Strong (50)')
        ax3.fill_between(plot_df.index, 0, 25, color='lightgray', alpha=0.3, label='Weak Trend')
        ax3.fill_between(plot_df.index, 25, 50, color='yellow', alpha=0.3, label='Strong Trend')
        ax3.fill_between(plot_df.index, 50, 100, color='red', alpha=0.3, label='Very Strong')
        ax3.set_title('Average Directional Index (Trend Strength)', fontsize=12, fontweight='bold')
        ax3.set_ylabel('ADX')
        ax3.set_ylim(0, 100)
        ax3.legend(fontsize=9)
        ax3.grid(True, alpha=0.3)

    # 4. 价格变化率分布
    if 'pct_change' in plot_df.columns:
        # 移除NaN值
        pct_changes = plot_df['pct_change'].dropna()
        ax4.hist(pct_changes, bins=30, color='lightblue', alpha=0.7, edgecolor='black')
        ax4.axvline(pct_changes.mean(), color='red', linestyle='--',
                   label=f'Mean: {pct_changes.mean():.3f}%')
        ax4.axvline(pct_changes.std(), color='orange', linestyle='--',
                   label=f'Std: {pct_changes.std():.3f}%')
        ax4.axvline(-pct_changes.std(), color='orange', linestyle='--')
        ax4.set_title('Price Change Distribution', fontsize=12, fontweight='bold')
        ax4.set_xlabel('Price Change (%)')
        ax4.set_ylabel('Frequency')
        ax4.legend(fontsize=9)
        ax4.grid(True, alpha=0.3)

    plt.suptitle(f'{SYMBOL} {INTERVAL} Trend & Volatility Analysis', fontsize=16, fontweight='bold')
    plt.tight_layout()
    plt.savefig(filename, dpi=150, bbox_inches='tight')
    plt.close()

    print(f"✅ Trend & volatility chart generated: {filename}")
    return filename


def generate_support_resistance_chart(df):
    """生成支撑阻力位专门图表"""
    filename = f"{SYMBOL}_{INTERVAL}_support_resistance.png"

    # 设置英文字体
    setup_english_fonts()

    # 只显示最新的数据点
    plot_df = df.iloc[-PLOT_LAST_POINTS:]
    last_row = plot_df.iloc[-1]

    # 创建图表
    plt.figure(figsize=(16, 10))

    # 主价格图表
    plt.subplot(2, 1, 1)
    plt.plot(plot_df.index, plot_df['close'], label='Close Price', color='black', linewidth=2)

    # 绘制所有支撑阻力位
    if 'key_support' in last_row and not np.isnan(last_row['key_support']):
        plt.axhline(y=last_row['key_support'], color='green', linestyle='-', linewidth=2,
                    label=f'Key Support: {last_row["key_support"]:.2f}', alpha=0.8)

    if 'key_resistance' in last_row and not np.isnan(last_row['key_resistance']):
        plt.axhline(y=last_row['key_resistance'], color='red', linestyle='-', linewidth=2,
                    label=f'Key Resistance: {last_row["key_resistance"]:.2f}', alpha=0.8)

    # 绘制枢轴点
    pivot_colors = {'s1': 'lightgreen', 's2': 'green', 'r1': 'lightcoral', 'r2': 'red'}
    for level, color in pivot_colors.items():
        if level in last_row and not pd.isna(last_row[level]):
            line_style = '--' if level in ['s2', 'r2'] else ':'
            plt.axhline(y=last_row[level], color=color, linestyle=line_style,
                       label=f'{level.upper()}: {last_row[level]:.2f}', alpha=0.7)

    # 添加价格区间填充
    if all(col in last_row for col in ['key_support', 'key_resistance']):
        plt.fill_between(plot_df.index, last_row['key_support'], last_row['key_resistance'],
                        color='yellow', alpha=0.1, label='Trading Range')

    plt.title(f'{SYMBOL} Support & Resistance Levels', fontsize=14, fontweight='bold')
    plt.ylabel('Price (USDT)')
    plt.legend(loc='best', fontsize=9)
    plt.grid(True, alpha=0.3)

    # 成交量图表
    plt.subplot(2, 1, 2)
    if 'volume' in plot_df.columns:
        colors = ['green' if plot_df['close'].iloc[i] >= plot_df['open'].iloc[i] else 'red'
                 for i in range(len(plot_df))]
        plt.bar(plot_df.index, plot_df['volume'], color=colors, alpha=0.7)
        plt.title('Trading Volume', fontsize=12)
        plt.ylabel('Volume')
        plt.grid(True, alpha=0.3)

    plt.tight_layout()
    plt.savefig(filename, dpi=150, bbox_inches='tight')
    plt.close()

    print(f"✅ Support & resistance chart generated: {filename}")
    return filename


def generate_momentum_chart(df):
    """生成动量指标图表"""
    filename = f"{SYMBOL}_{INTERVAL}_momentum.png"

    # 设置英文字体
    setup_english_fonts()

    # 只显示最新的数据点
    plot_df = df.iloc[-PLOT_LAST_POINTS:]

    # 创建图表
    fig, axes = plt.subplots(3, 1, figsize=(16, 12))

    # 1. RSI
    if 'RSI' in plot_df.columns:
        axes[0].plot(plot_df.index, plot_df['RSI'], label='RSI(14)', color='purple', linewidth=2)
        axes[0].axhline(70, color='red', linestyle='--', label='Overbought (70)')
        axes[0].axhline(30, color='green', linestyle='--', label='Oversold (30)')
        axes[0].axhline(50, color='gray', linestyle='-', alpha=0.5, label='Neutral (50)')
        axes[0].fill_between(plot_df.index, 30, 70, color='lightgray', alpha=0.2)
        axes[0].set_title('RSI - Relative Strength Index', fontsize=12, fontweight='bold')
        axes[0].set_ylabel('RSI')
        axes[0].set_ylim(0, 100)
        axes[0].legend(fontsize=9)
        axes[0].grid(True, alpha=0.3)

    # 2. MACD
    if all(col in plot_df.columns for col in ['MACD', 'MACD_signal', 'MACD_hist']):
        axes[1].plot(plot_df.index, plot_df['MACD'], label='MACD', color='blue', linewidth=2)
        axes[1].plot(plot_df.index, plot_df['MACD_signal'], label='Signal', color='red', linewidth=2)

        # MACD柱状图
        colors = ['green' if x >= 0 else 'red' for x in plot_df['MACD_hist']]
        axes[1].bar(plot_df.index, plot_df['MACD_hist'], color=colors, alpha=0.6, label='Histogram')

        axes[1].axhline(0, color='gray', linestyle='-', alpha=0.5)
        axes[1].set_title('MACD - Moving Average Convergence Divergence', fontsize=12, fontweight='bold')
        axes[1].set_ylabel('MACD')
        axes[1].legend(fontsize=9)
        axes[1].grid(True, alpha=0.3)

    # 3. 随机指标 (如果有的话)
    if all(col in plot_df.columns for col in ['STOCH_k', 'STOCH_d']):
        axes[2].plot(plot_df.index, plot_df['STOCH_k'], label='%K', color='blue', linewidth=2)
        axes[2].plot(plot_df.index, plot_df['STOCH_d'], label='%D', color='red', linewidth=2)
        axes[2].axhline(80, color='red', linestyle='--', label='Overbought (80)')
        axes[2].axhline(20, color='green', linestyle='--', label='Oversold (20)')
        axes[2].fill_between(plot_df.index, 20, 80, color='lightgray', alpha=0.2)
        axes[2].set_title('Stochastic Oscillator', fontsize=12, fontweight='bold')
        axes[2].set_ylabel('Stochastic')
        axes[2].set_ylim(0, 100)
        axes[2].legend(fontsize=9)
        axes[2].grid(True, alpha=0.3)
    else:
        # 如果没有随机指标，显示价格变化率
        if 'pct_change' in plot_df.columns:
            axes[2].plot(plot_df.index, plot_df['pct_change'], label='Price Change %',
                        color='orange', linewidth=2)
            axes[2].axhline(0, color='gray', linestyle='-', alpha=0.5)
            axes[2].fill_between(plot_df.index, 0, plot_df['pct_change'],
                               where=(plot_df['pct_change'] >= 0), color='green', alpha=0.3)
            axes[2].fill_between(plot_df.index, 0, plot_df['pct_change'],
                               where=(plot_df['pct_change'] < 0), color='red', alpha=0.3)
            axes[2].set_title('Price Change Rate', fontsize=12, fontweight='bold')
            axes[2].set_ylabel('Change %')
            axes[2].legend(fontsize=9)
            axes[2].grid(True, alpha=0.3)

    plt.suptitle(f'{SYMBOL} Momentum Indicators', fontsize=16, fontweight='bold')
    plt.tight_layout()
    plt.savefig(filename, dpi=150, bbox_inches='tight')
    plt.close()

    print(f"✅ Momentum indicators chart generated: {filename}")
    return filename


def generate_technical_chart(df):
    """生成技术分析图表 - 兼容旧接口"""
    return plot_technical_analysis(df)

def generate_analysis_guide():
    """生成提问指南"""
    filename = "analysis_guide.txt"
    
    content = f"""
请分析提供的交易数据文件，完成以下任务：

1. 支撑位/阻力位分析
   - 识别当前价格附近的关键支撑位和阻力位
   - 评估每个支撑位/阻力位的强度
   - 预测这些水平位的可靠性

2. 趋势分析
   - 确定当前市场趋势方向（上涨/下跌/盘整）
   - 评估趋势强度（基于ADX和价格行为）
   - 识别潜在的反转信号

3. 交易策略建议
   - 提供明确的做多/做空建议
   - 建议具体的入场点位
   - 设置止损位和目标位
   - 计算风险/回报比

4. 风险管理
   - 基于ATR建议止损距离
   - 建议仓位大小
   - 识别潜在风险事件

5. 图表解读
   - 描述图表中的关键形态
   - 分析异常交易量区域
   - 标记图表中的关键水平位

数据说明：
- 主数据文件: {SYMBOL}_{INTERVAL}_{DAYS_TO_ANALYZE}days_data.csv
- 包含指标: 开盘价, 最高价, 最低价, 收盘价, 成交量, RSI, MACD, 布林带, ATR, 支撑位/阻力位等
- 时间范围: 最近{DAYS_TO_ANALYZE}天
- K线周期: {INTERVAL}

输出要求:
- 使用中文回复
- 结构清晰，分为以上5个部分
- 包含具体数值和价格水平
- 给出可操作的交易建议
"""
    
    with open(filename, "w", encoding="utf-8") as f:
        f.write(content)
    
    print(f"提问指南已生成: {filename}")
    return filename