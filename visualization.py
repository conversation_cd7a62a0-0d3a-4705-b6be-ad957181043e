# visualization.py
import matplotlib.pyplot as plt
import pandas as pd
import numpy as np
import os
from datetime import datetime
from config import SYMBOL, INTERVAL, DAYS_TO_ANALYZE, PLOT_LAST_POINTS

# 设置中文支持和字体
plt.rcParams['font.family'] = ['DejaVu Sans', 'SimHei', 'Microsoft YaHei', 'Arial Unicode MS', 'sans-serif']
plt.rcParams['font.sans-serif'] = ['DejaVu Sans', 'SimHei', 'Microsoft YaHei', 'Arial Unicode MS']
plt.rcParams['axes.unicode_minus'] = False
plt.rcParams['font.size'] = 10
plt.rcParams['axes.titlesize'] = 12
plt.rcParams['axes.labelsize'] = 10
plt.rcParams['legend.fontsize'] = 9

# 测试字体是否可用
def test_font():
    """测试字体设置"""
    try:
        fig, ax = plt.subplots(figsize=(1, 1))
        ax.text(0.5, 0.5, '测试中文', fontsize=12)
        plt.close(fig)
        print("✅ 中文字体设置成功")
        return True
    except Exception as e:
        print(f"⚠️ 字体设置可能有问题: {e}")
        return False

def save_main_data_file(df):
    """保存主数据文件 - 优化格式便于AI分析"""
    filename = f"{SYMBOL}_{INTERVAL}_{DAYS_TO_ANALYZE}days_data.csv"

    # 创建优化的数据副本
    df_export = df.copy()

    # 重命名列为英文，便于DeepSeek理解
    column_mapping = {
        'open': 'Open_Price',
        'high': 'High_Price',
        'low': 'Low_Price',
        'close': 'Close_Price',
        'volume': 'Volume',
        'pct_change': 'Price_Change_Pct',
        'RSI': 'RSI_14',
        'MACD': 'MACD_Line',
        'MACD_signal': 'MACD_Signal',
        'MACD_hist': 'MACD_Histogram',
        'SMA_20': 'SMA_20_Period',
        'SMA_50': 'SMA_50_Period',
        'SMA_200': 'SMA_200_Period',
        'EMA_12': 'EMA_12_Period',
        'EMA_26': 'EMA_26_Period',
        'BB_upper': 'Bollinger_Upper',
        'BB_middle': 'Bollinger_Middle',
        'BB_lower': 'Bollinger_Lower',
        'ATR': 'ATR_14',
        'ADX': 'ADX_14',
        'key_support': 'Key_Support_Level',
        'key_resistance': 'Key_Resistance_Level',
        'trade_suggestion': 'Trade_Suggestion'
    }

    # 只保留存在的列并重命名
    existing_columns = {k: v for k, v in column_mapping.items() if k in df_export.columns}
    df_export = df_export[list(existing_columns.keys())].rename(columns=existing_columns)

    # 格式化数值，保留合适的小数位数
    for col in df_export.columns:
        if df_export[col].dtype in ['float64', 'float32']:
            if 'Price' in col or 'Level' in col:
                df_export[col] = df_export[col].round(2)  # 价格保留2位小数
            elif 'Volume' in col:
                df_export[col] = df_export[col].round(0).astype('int64')  # 成交量取整
            elif 'Pct' in col:
                df_export[col] = df_export[col].round(3)  # 百分比保留3位小数
            else:
                df_export[col] = df_export[col].round(4)  # 其他指标保留4位小数

    # 添加时间列（便于分析）
    df_export.insert(0, 'DateTime', df_export.index.strftime('%Y-%m-%d %H:%M:%S'))
    df_export.insert(1, 'Date', df_export.index.strftime('%Y-%m-%d'))
    df_export.insert(2, 'Hour', df_export.index.hour)

    # 限制数据量，只保留最近的数据（避免文件过大）
    max_rows = 168  # 7天 * 24小时 = 168行，适中的数据量
    if len(df_export) > max_rows:
        df_export = df_export.tail(max_rows)
        print(f"⚠️ 数据量较大，只保留最近{max_rows}条记录（约7天数据）")

    # 保存为标准CSV格式
    df_export.to_csv(filename, index=False, encoding='utf-8-sig',
                     float_format='%.4f', date_format='%Y-%m-%d %H:%M:%S')

    # 显示文件信息
    file_size = os.path.getsize(filename) / 1024  # KB
    print(f"✅ 主数据文件已生成: {filename}")
    print(f"   - 数据行数: {len(df_export)}")
    print(f"   - 数据列数: {len(df_export.columns)}")
    print(f"   - 文件大小: {file_size:.1f} KB")
    print(f"   - 时间范围: {df_export['DateTime'].iloc[0]} 到 {df_export['DateTime'].iloc[-1]}")

    return filename

def generate_analysis_summary(df):
    """生成分析摘要文件"""
    filename = f"{SYMBOL}_{INTERVAL}_analysis_summary.md"
    last_row = df.iloc[-1]
    
    # 计算24小时变化率
    last_24h_change = df['pct_change'][-24:].sum() if len(df) >= 24 else np.nan
    
    content = f"""
# {SYMBOL} {INTERVAL} 技术分析摘要 ({DAYS_TO_ANALYZE}天数据)

**报告生成时间**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

## 关键指标
| 指标 | 值 | 状态 |
|------|----|------|
| 当前价格 | {last_row['close']} | - |
| 24小时变化 | {last_24h_change:.2f}% | {'↑' if last_24h_change > 0 else '↓' if not np.isnan(last_24h_change) else 'N/A'} |
| RSI (14) | {last_row.get('RSI', 'N/A'):.2f} | {'超买' if last_row.get('RSI', 0) > 70 else '超卖' if last_row.get('RSI', 0) < 30 else '正常'} |
| MACD | {last_row.get('MACD', 'N/A'):.4f} | {'看涨' if last_row.get('MACD', 0) > last_row.get('MACD_signal', 0) else '看跌'} |
| ADX | {last_row.get('ADX', 'N/A'):.2f} | {'强趋势' if last_row.get('ADX', 0) > 25 else '弱趋势'} |
| ATR | {last_row.get('ATR', 'N/A'):.4f} | 波动率 |

## 支撑位与阻力位
### 关键支撑位
1. {last_row.get('key_support', 'N/A'):.4f} (主要支撑)
2. {last_row.get('s1', 'N/A'):.4f} (次要支撑)
3. {last_row.get('s2', 'N/A'):.4f} (强支撑)

### 关键阻力位
1. {last_row.get('key_resistance', 'N/A'):.4f} (主要阻力)
2. {last_row.get('r1', 'N/A'):.4f} (次要阻力)
3. {last_row.get('r2', 'N/A'):.4f} (强阻力)

## 交易建议
{last_row.get('trade_suggestion', '暂无建议')}

> 完整数据请查看附件CSV文件
"""

    with open(filename, "w", encoding="utf-8") as f:
        f.write(content)
    
    print(f"分析摘要已生成: {filename}")
    return filename

def generate_technical_chart(df):
    """生成技术分析图表"""
    filename = f"{SYMBOL}_{INTERVAL}_technical_analysis.png"

    # 测试字体
    test_font()

    # 只显示最新的数据点
    plot_df = df.iloc[-PLOT_LAST_POINTS:]
    last_row = plot_df.iloc[-1]

    # 创建图表，使用更大的尺寸和更高的DPI
    plt.figure(figsize=(20, 16), dpi=100)
    plt.style.use('default')  # 使用默认样式确保文字显示
    
    # 价格图表
    plt.subplot(3, 1, 1)
    plt.plot(plot_df.index, plot_df['close'], label='价格', color='black', linewidth=1.5)
    
    # 绘制关键支撑位和阻力位
    if 'key_support' in last_row and not np.isnan(last_row['key_support']):
        plt.axhline(y=last_row['key_support'], color='green', linestyle='--', label=f'关键支撑位: {last_row["key_support"]:.4f}')
    if 'key_resistance' in last_row and not np.isnan(last_row['key_resistance']):
        plt.axhline(y=last_row['key_resistance'], color='red', linestyle='--', label=f'关键阻力位: {last_row["key_resistance"]:.4f}')
    
    # 绘制移动平均线
    for col in ['SMA_20', 'SMA_50', 'EMA_12', 'EMA_26']:
        if col in plot_df.columns:
            plt.plot(plot_df.index, plot_df[col], label=col, linestyle='--' if 'SMA' in col else '-')
    
    plt.title(f'{SYMBOL} {INTERVAL} Price and Technical Indicators', fontsize=14, fontweight='bold')
    plt.ylabel('Price (USDT)', fontsize=12)
    plt.legend(loc='best', fontsize=10)
    plt.grid(True, linestyle='--', alpha=0.7)

    # 添加价格标注
    current_price = plot_df['close'].iloc[-1]
    plt.text(plot_df.index[-1], current_price, f'Current: ${current_price:.2f}',
             fontsize=10, bbox=dict(boxstyle="round,pad=0.3", facecolor="yellow", alpha=0.7))
    
    # RSI和MACD
    plt.subplot(3, 1, 2)
    
    # RSI
    if 'RSI' in plot_df.columns:
        plt.plot(plot_df.index, plot_df['RSI'], label='RSI', color='purple', linewidth=2)
        plt.axhline(70, color='red', linestyle='--', label='Overbought (70)', alpha=0.8)
        plt.axhline(30, color='green', linestyle='--', label='Oversold (30)', alpha=0.8)
        plt.fill_between(plot_df.index, 30, 70, color='yellow', alpha=0.1, label='Normal Range')
        plt.ylim(0, 100)
        plt.ylabel('RSI', fontsize=12)
        plt.title('RSI Indicator', fontsize=12)
        plt.legend(loc='best', fontsize=10)
        plt.grid(True, linestyle='--', alpha=0.7)

        # 添加当前RSI值标注
        current_rsi = plot_df['RSI'].iloc[-1]
        plt.text(plot_df.index[-1], current_rsi, f'RSI: {current_rsi:.1f}',
                fontsize=10, bbox=dict(boxstyle="round,pad=0.3", facecolor="lightblue", alpha=0.7))
    
    # MACD
    plt.subplot(3, 1, 3)
    if 'MACD' in plot_df.columns and 'MACD_signal' in plot_df.columns:
        plt.plot(plot_df.index, plot_df['MACD'], label='MACD', color='blue', linewidth=2)
        plt.plot(plot_df.index, plot_df['MACD_signal'], label='Signal Line', color='red', linewidth=2)

        # MACD柱状图
        if 'MACD_hist' in plot_df.columns:
            colors = np.where(plot_df['MACD_hist'] >= 0, 'green', 'red')
            plt.bar(plot_df.index, plot_df['MACD_hist'], color=colors, alpha=0.6, label='MACD Histogram')

        plt.axhline(0, color='gray', linestyle='-', alpha=0.5)
        plt.ylabel('MACD', fontsize=12)
        plt.title('MACD Indicator', fontsize=12)

        # 添加当前MACD值标注
        current_macd = plot_df['MACD'].iloc[-1]
        plt.text(plot_df.index[-1], current_macd, f'MACD: {current_macd:.2f}',
                fontsize=10, bbox=dict(boxstyle="round,pad=0.3", facecolor="lightgreen", alpha=0.7))
        plt.legend(loc='best', fontsize=10)
        plt.grid(True, linestyle='--', alpha=0.7)

    # 添加总标题
    plt.suptitle(f'{SYMBOL} {INTERVAL} Technical Analysis - {datetime.now().strftime("%Y-%m-%d %H:%M")}',
                fontsize=16, fontweight='bold', y=0.98)

    plt.tight_layout()
    plt.subplots_adjust(top=0.95)  # 为总标题留出空间

    # 保存图片，使用高DPI和优化设置
    plt.savefig(filename, dpi=150, bbox_inches='tight', facecolor='white',
                edgecolor='none', format='png')
    plt.close()
    
    print(f"技术分析图表已生成: {filename}")
    return filename

def generate_analysis_guide():
    """生成提问指南"""
    filename = "analysis_guide.txt"
    
    content = f"""
请分析提供的交易数据文件，完成以下任务：

1. 支撑位/阻力位分析
   - 识别当前价格附近的关键支撑位和阻力位
   - 评估每个支撑位/阻力位的强度
   - 预测这些水平位的可靠性

2. 趋势分析
   - 确定当前市场趋势方向（上涨/下跌/盘整）
   - 评估趋势强度（基于ADX和价格行为）
   - 识别潜在的反转信号

3. 交易策略建议
   - 提供明确的做多/做空建议
   - 建议具体的入场点位
   - 设置止损位和目标位
   - 计算风险/回报比

4. 风险管理
   - 基于ATR建议止损距离
   - 建议仓位大小
   - 识别潜在风险事件

5. 图表解读
   - 描述图表中的关键形态
   - 分析异常交易量区域
   - 标记图表中的关键水平位

数据说明：
- 主数据文件: {SYMBOL}_{INTERVAL}_{DAYS_TO_ANALYZE}days_data.csv
- 包含指标: 开盘价, 最高价, 最低价, 收盘价, 成交量, RSI, MACD, 布林带, ATR, 支撑位/阻力位等
- 时间范围: 最近{DAYS_TO_ANALYZE}天
- K线周期: {INTERVAL}

输出要求:
- 使用中文回复
- 结构清晰，分为以上5个部分
- 包含具体数值和价格水平
- 给出可操作的交易建议
"""
    
    with open(filename, "w", encoding="utf-8") as f:
        f.write(content)
    
    print(f"提问指南已生成: {filename}")
    return filename