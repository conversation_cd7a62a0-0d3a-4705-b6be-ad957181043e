# visualization.py
import matplotlib.pyplot as plt
from matplotlib.gridspec import GridSpec
import numpy as np
import pandas as pd
from config import PLOT_LAST_POINTS, SYMBOL, INTERVAL

# 设置中文支持
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'Arial Unicode MS']
plt.rcParams['axes.unicode_minus'] = False


def plot_technical_analysis(df):
    """绘制技术分析图表（含支撑/压力位）"""
    # 只显示最新的数据点
    plot_df = df.iloc[-PLOT_LAST_POINTS:]
    last_row = plot_df.iloc[-1]

    # 创建图表布局
    plt.figure(figsize=(16, 14), dpi=100)
    plt.suptitle(f'{SYMBOL} 技术分析 - {INTERVAL} 图表', fontsize=18, fontweight='bold')
    gs = GridSpec(5, 1, height_ratios=[3, 1, 1, 1, 1])

    # 1. 价格和关键水平
    ax1 = plt.subplot(gs[0])
    ax1.plot(plot_df.index, plot_df['close'], label='收盘价', color='black', linewidth=1.5)

    # 绘制关键支撑位和阻力位
    ax1.axhline(y=last_row['key_support'], color='green', linestyle='--', alpha=0.7,
                label=f'关键支撑位: {last_row["key_support"]:.4f}')
    ax1.axhline(y=last_row['key_resistance'], color='red', linestyle='--', alpha=0.7,
                label=f'关键阻力位: {last_row["key_resistance"]:.4f}')

    # 绘制移动平均线
    for col in plot_df.columns:
        if 'SMA_' in col:
            period = col.split('_')[-1]
            ax1.plot(plot_df.index, plot_df[col], label=f'{period}日简单移动平均', linestyle='--', alpha=0.8)

    # 绘制布林带
    if 'BB_upper' in plot_df.columns:
        ax1.fill_between(plot_df.index, plot_df['BB_lower'], plot_df['BB_upper'],
                         color='lightblue', alpha=0.3, label='布林带')

    ax1.set_title(f'价格与关键水平 | 交易建议: {last_row["trade_suggestion"]}', fontsize=14)
    ax1.set_ylabel('价格')
    ax1.legend(loc='best', fontsize=9)
    ax1.grid(True, linestyle='--', alpha=0.7)

    # 2. RSI指标
    ax2 = plt.subplot(gs[1], sharex=ax1)
    ax2.plot(plot_df.index, plot_df['RSI'], label='RSI', color='purple', linewidth=1.2)
    ax2.axhline(70, color='red', linestyle='--', alpha=0.5, label='超买线 (70)')
    ax2.axhline(50, color='gray', linestyle='-', alpha=0.3)
    ax2.axhline(30, color='green', linestyle='--', alpha=0.5, label='超卖线 (30)')
    ax2.fill_between(plot_df.index, 30, 70, color='yellow', alpha=0.1, label='正常区域')
    ax2.set_ylabel('相对强弱指数 (RSI)')
    ax2.set_ylim(0, 100)
    ax2.legend(loc='best', fontsize=9)
    ax2.grid(True, linestyle='--', alpha=0.7)

    # 3. MACD指标
    ax3 = plt.subplot(gs[2], sharex=ax1)
    ax3.plot(plot_df.index, plot_df['MACD'], label='MACD线', color='blue', linewidth=1.2)
    ax3.plot(plot_df.index, plot_df['MACD_signal'], label='信号线', color='red', linewidth=1.2)

    # 创建MACD柱状图（红绿表示）
    colors = np.where(plot_df['MACD_hist'] >= 0, 'green', 'red')
    ax3.bar(plot_df.index, plot_df['MACD_hist'], color=colors, alpha=0.5, label='MACD柱状图')

    ax3.axhline(0, color='gray', linestyle='-', alpha=0.5)
    ax3.set_title('指数平滑异同移动平均线 (MACD)', fontsize=14)
    ax3.set_ylabel('MACD值')
    ax3.legend(loc='best', fontsize=9)
    ax3.grid(True, linestyle='--', alpha=0.7)

    # 4. 支撑位和阻力位详情
    ax4 = plt.subplot(gs[3], sharex=ax1)

    # 绘制各种支撑位
    ax4.plot(plot_df.index, plot_df['recent_low'], label='近期低点', color='green', alpha=0.7)
    ax4.plot(plot_df.index, plot_df['s1'], label='支撑位1 (S1)', color='lightgreen', linestyle='--')
    ax4.plot(plot_df.index, plot_df['s2'], label='支撑位2 (S2)', color='green', linestyle='--')

    # 绘制各种阻力位
    ax4.plot(plot_df.index, plot_df['recent_high'], label='近期高点', color='red', alpha=0.7)
    ax4.plot(plot_df.index, plot_df['r1'], label='阻力位1 (R1)', color='salmon', linestyle='--')
    ax4.plot(plot_df.index, plot_df['r2'], label='阻力位2 (R2)', color='red', linestyle='--')

    ax4.set_title('详细支撑位和阻力位', fontsize=14)
    ax4.set_ylabel('价格水平')
    ax4.legend(loc='best', fontsize=9)
    ax4.grid(True, linestyle='--', alpha=0.7)

    # 5. 成交量
    ax5 = plt.subplot(gs[4], sharex=ax1)
    # 根据价格涨跌着色
    colors = np.where(plot_df['close'] > plot_df['open'], 'green', 'red')
    ax5.bar(plot_df.index, plot_df['volume'], color=colors, alpha=0.7, label='成交量')

    # 添加OBV线
    if 'OBV' in plot_df.columns:
        ax5b = ax5.twinx()
        ax5b.plot(plot_df.index, plot_df['OBV'], label='能量潮 (OBV)', color='purple', linewidth=1.5)
        ax5b.set_ylabel('OBV值')
        ax5b.legend(loc='upper right', fontsize=9)

    ax5.set_title('成交量分析', fontsize=14)
    ax5.set_ylabel('成交量')
    ax5.legend(loc='upper left', fontsize=9)
    ax5.grid(True, linestyle='--', alpha=0.7)

    # 设置x轴格式
    plt.gcf().autofmt_xdate()
    plt.tight_layout()
    plt.subplots_adjust(top=0.95)
    plt.savefig(f'{SYMBOL}_{INTERVAL}_analysis.png', dpi=150, bbox_inches='tight')
    plt.show()

    # 单独绘制ADX和ATR
    plt.figure(figsize=(16, 6), dpi=100)
    plt.suptitle('趋势与波动指标', fontsize=16, fontweight='bold')

    plt.subplot(1, 2, 1)
    plt.plot(plot_df.index, plot_df['ADX'], label='ADX', color='blue', linewidth=1.5)
    plt.plot(plot_df.index, [25] * len(plot_df), 'r--', label='趋势强度阈值', alpha=0.5)
    plt.title('平均趋向指数 (ADX)')
    plt.ylabel('ADX值')
    plt.legend()
    plt.grid(True, linestyle='--', alpha=0.7)

    plt.subplot(1, 2, 2)
    plt.plot(plot_df.index, plot_df['ATR'], label='ATR', color='red', linewidth=1.5)
    plt.title('平均真实波幅 (ATR)')
    plt.ylabel('ATR值')
    plt.legend()
    plt.grid(True, linestyle='--', alpha=0.7)

    plt.tight_layout()
    plt.subplots_adjust(top=0.9)
    plt.savefig(f'{SYMBOL}_{INTERVAL}_trend_volatility.png', dpi=150, bbox_inches='tight')
    plt.show()


def generate_analysis_report(df, symbol, interval):
    """生成DeepSeek分析报告"""
    last_row = df.iloc[-1]
    report = f"""
# {symbol} {interval} 技术分析报告

## 当前市场状态
- **当前价格**: {last_row['close']}
- **关键支撑位**: {last_row['key_support']}
- **关键阻力位**: {last_row['key_resistance']}
- **价格位置**: 处于支撑位上方 {((last_row['close'] - last_row['key_support']) / last_row['key_support'] * 100):.2f}%
- **交易建议**: {last_row['trade_suggestion']}

## 技术指标分析
- **相对强弱指数 (RSI)**: {last_row['RSI']:.2f} - {'超买' if last_row['RSI'] > 70 else '超卖' if last_row['RSI'] < 30 else '中性'}
- **MACD**: {last_row['MACD']:.4f} | 信号线: {last_row['MACD_signal']:.4f} - {'看涨' if last_row['MACD'] > last_row['MACD_signal'] else '看跌'}
- **平均趋向指数 (ADX)**: {last_row['ADX']:.2f} - {'强趋势' if last_row['ADX'] > 25 else '弱趋势'}
- **平均真实波幅 (ATR)**: {last_row['ATR']:.4f} - 当前市场波动率

## 支撑位和阻力位分析
### 主要支撑位
1. **近期低点**: {last_row['recent_low']}
2. **枢轴支撑位1 (S1)**: {last_row['s1']}
3. **枢轴支撑位2 (S2)**: {last_row['s2']}

### 主要阻力位
1. **近期高点**: {last_row['recent_high']}
2. **枢轴阻力位1 (R1)**: {last_row['r1']}
3. **枢轴阻力位2 (R2)**: {last_row['r2']}

## 交易建议
{get_trade_recommendation(last_row)}

## 附件说明
1. `{symbol}_{interval}_data.csv` - 包含所有技术指标和支撑/阻力位的完整数据
2. `{symbol}_{interval}_analysis.png` - 技术分析图表
3. `{symbol}_{interval}_trend_volatility.png` - 趋势与波动指标图表

请将数据文件作为附件发送给DeepSeek进行深入分析。
"""

    with open('analysis_report.md', 'w', encoding='utf-8') as f:
        f.write(report)

    print("分析报告已生成: analysis_report.md")


def get_trade_recommendation(last_row):
    """生成交易建议"""
    recommendation = ""

    # 基于价格位置
    price_position = last_row['price_position']
    if price_position < 0.3:
        recommendation += "- **价格位置**: 接近支撑位，适合寻找做多机会\n"
    elif price_position > 0.7:
        recommendation += "- **价格位置**: 接近阻力位，适合寻找做空机会\n"
    else:
        recommendation += "- **价格位置**: 处于中间区域，建议等待突破\n"

    # 基于RSI
    if last_row['RSI'] < 35:
        recommendation += "- **RSI指标**: 接近超卖区域，可能反弹，适合做多\n"
    elif last_row['RSI'] > 65:
        recommendation += "- **RSI指标**: 接近超买区域，可能回调，适合做空\n"

    # 基于MACD
    if last_row['MACD'] > last_row['MACD_signal']:
        recommendation += "- **MACD指标**: 金叉形态，短期看涨\n"
    else:
        recommendation += "- **MACD指标**: 死叉形态，短期看跌\n"

    # 基于趋势强度
    if last_row['ADX'] > 30:
        recommendation += "- **趋势强度**: 强趋势，适合顺势交易\n"
    else:
        recommendation += "- **趋势强度**: 弱趋势，适合区间交易\n"

    # 风险管理建议
    recommendation += "\n## 风险管理建议\n"
    recommendation += f"- **止损位置**: 做多止损在支撑位下方 {last_row['key_support'] * 0.995:.4f}，做空止损在阻力位上方 {last_row['key_resistance'] * 1.005:.4f}\n"
    recommendation += f"- **止盈目标**: 做多目标在阻力位 {last_row['key_resistance']:.4f}，做空目标在支撑位 {last_row['key_support']:.4f}\n"
    recommendation += "- **仓位管理**: 单笔交易风险不超过总资金的2%\n"

    return recommendation