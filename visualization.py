# visualization.py
import matplotlib.pyplot as plt
import pandas as pd
import numpy as np
import os
from datetime import datetime
from config import SYMBOL, INTERVAL, DAYS_TO_ANALYZE, PLOT_LAST_POINTS

# 设置中文支持
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'Arial Unicode MS']
plt.rcParams['axes.unicode_minus'] = False

def save_main_data_file(df):
    """保存主数据文件"""
    filename = f"{SYMBOL}_{INTERVAL}_{DAYS_TO_ANALYZE}days_data.csv"
    
    # 优化列名和格式
    df = df.copy()
    df.index.name = 'timestamp'
    
    # 添加分析元数据
    metadata = pd.DataFrame({
        'symbol': [SYMBOL],
        'interval': [INTERVAL],
        'days': [DAYS_TO_ANALYZE],
        'last_update': [datetime.now().strftime('%Y-%m-%d %H:%M:%S')]
    })
    
    # 保存文件
    with open(filename, 'w', encoding='utf-8-sig') as f:
        f.write("# DeepSeek Analysis Data File\n")
        f.write("# METADATA_START\n")
        metadata.to_csv(f, index=False, header=True)
        f.write("# METADATA_END\n")
        df.to_csv(f, header=True)
    
    print(f"主数据文件已生成: {filename}")
    return filename

def generate_analysis_summary(df):
    """生成分析摘要文件"""
    filename = f"{SYMBOL}_{INTERVAL}_analysis_summary.md"
    last_row = df.iloc[-1]
    
    # 计算24小时变化率
    last_24h_change = df['pct_change'][-24:].sum() if len(df) >= 24 else np.nan
    
    content = f"""
# {SYMBOL} {INTERVAL} 技术分析摘要 ({DAYS_TO_ANALYZE}天数据)

**报告生成时间**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

## 关键指标
| 指标 | 值 | 状态 |
|------|----|------|
| 当前价格 | {last_row['close']} | - |
| 24小时变化 | {last_24h_change:.2f}% | {'↑' if last_24h_change > 0 else '↓' if not np.isnan(last_24h_change) else 'N/A'} |
| RSI (14) | {last_row.get('RSI', 'N/A'):.2f} | {'超买' if last_row.get('RSI', 0) > 70 else '超卖' if last_row.get('RSI', 0) < 30 else '正常'} |
| MACD | {last_row.get('MACD', 'N/A'):.4f} | {'看涨' if last_row.get('MACD', 0) > last_row.get('MACD_signal', 0) else '看跌'} |
| ADX | {last_row.get('ADX', 'N/A'):.2f} | {'强趋势' if last_row.get('ADX', 0) > 25 else '弱趋势'} |
| ATR | {last_row.get('ATR', 'N/A'):.4f} | 波动率 |

## 支撑位与阻力位
### 关键支撑位
1. {last_row.get('key_support', 'N/A'):.4f} (主要支撑)
2. {last_row.get('s1', 'N/A'):.4f} (次要支撑)
3. {last_row.get('s2', 'N/A'):.4f} (强支撑)

### 关键阻力位
1. {last_row.get('key_resistance', 'N/A'):.4f} (主要阻力)
2. {last_row.get('r1', 'N/A'):.4f} (次要阻力)
3. {last_row.get('r2', 'N/A'):.4f} (强阻力)

## 交易建议
{last_row.get('trade_suggestion', '暂无建议')}

> 完整数据请查看附件CSV文件
"""

    with open(filename, "w", encoding="utf-8") as f:
        f.write(content)
    
    print(f"分析摘要已生成: {filename}")
    return filename

def generate_technical_chart(df):
    """生成技术分析图表"""
    filename = f"{SYMBOL}_{INTERVAL}_technical_analysis.png"
    
    # 只显示最新的数据点
    plot_df = df.iloc[-PLOT_LAST_POINTS:]
    last_row = plot_df.iloc[-1]
    
    # 创建图表
    plt.figure(figsize=(16, 12))
    
    # 价格图表
    plt.subplot(3, 1, 1)
    plt.plot(plot_df.index, plot_df['close'], label='价格', color='black', linewidth=1.5)
    
    # 绘制关键支撑位和阻力位
    if 'key_support' in last_row and not np.isnan(last_row['key_support']):
        plt.axhline(y=last_row['key_support'], color='green', linestyle='--', label=f'关键支撑位: {last_row["key_support"]:.4f}')
    if 'key_resistance' in last_row and not np.isnan(last_row['key_resistance']):
        plt.axhline(y=last_row['key_resistance'], color='red', linestyle='--', label=f'关键阻力位: {last_row["key_resistance"]:.4f}')
    
    # 绘制移动平均线
    for col in ['SMA_20', 'SMA_50', 'EMA_12', 'EMA_26']:
        if col in plot_df.columns:
            plt.plot(plot_df.index, plot_df[col], label=col, linestyle='--' if 'SMA' in col else '-')
    
    plt.title(f'{SYMBOL} {INTERVAL} 价格与技术指标')
    plt.ylabel('价格')
    plt.legend(loc='best')
    plt.grid(True, linestyle='--', alpha=0.7)
    
    # RSI和MACD
    plt.subplot(3, 1, 2)
    
    # RSI
    if 'RSI' in plot_df.columns:
        plt.plot(plot_df.index, plot_df['RSI'], label='RSI', color='purple')
        plt.axhline(70, color='red', linestyle='--', label='超买线 (70)')
        plt.axhline(30, color='green', linestyle='--', label='超卖线 (30)')
        plt.fill_between(plot_df.index, 30, 70, color='yellow', alpha=0.1, label='正常区域')
        plt.ylim(0, 100)
        plt.ylabel('RSI')
        plt.legend(loc='best')
        plt.grid(True, linestyle='--', alpha=0.7)
    
    # MACD
    plt.subplot(3, 1, 3)
    if 'MACD' in plot_df.columns and 'MACD_signal' in plot_df.columns:
        plt.plot(plot_df.index, plot_df['MACD'], label='MACD', color='blue')
        plt.plot(plot_df.index, plot_df['MACD_signal'], label='信号线', color='red')
        
        # MACD柱状图
        if 'MACD_hist' in plot_df.columns:
            colors = np.where(plot_df['MACD_hist'] >= 0, 'green', 'red')
            plt.bar(plot_df.index, plot_df['MACD_hist'], color=colors, alpha=0.5, label='MACD柱状图')
        
        plt.axhline(0, color='gray', linestyle='-', alpha=0.5)
        plt.ylabel('MACD')
        plt.legend(loc='best')
        plt.grid(True, linestyle='--', alpha=0.7)
    
    plt.tight_layout()
    plt.savefig(filename, dpi=150)
    plt.close()
    
    print(f"技术分析图表已生成: {filename}")
    return filename

def generate_analysis_guide():
    """生成提问指南"""
    filename = "analysis_guide.txt"
    
    content = f"""
请分析提供的交易数据文件，完成以下任务：

1. 支撑位/阻力位分析
   - 识别当前价格附近的关键支撑位和阻力位
   - 评估每个支撑位/阻力位的强度
   - 预测这些水平位的可靠性

2. 趋势分析
   - 确定当前市场趋势方向（上涨/下跌/盘整）
   - 评估趋势强度（基于ADX和价格行为）
   - 识别潜在的反转信号

3. 交易策略建议
   - 提供明确的做多/做空建议
   - 建议具体的入场点位
   - 设置止损位和目标位
   - 计算风险/回报比

4. 风险管理
   - 基于ATR建议止损距离
   - 建议仓位大小
   - 识别潜在风险事件

5. 图表解读
   - 描述图表中的关键形态
   - 分析异常交易量区域
   - 标记图表中的关键水平位

数据说明：
- 主数据文件: {SYMBOL}_{INTERVAL}_{DAYS_TO_ANALYZE}days_data.csv
- 包含指标: 开盘价, 最高价, 最低价, 收盘价, 成交量, RSI, MACD, 布林带, ATR, 支撑位/阻力位等
- 时间范围: 最近{DAYS_TO_ANALYZE}天
- K线周期: {INTERVAL}

输出要求:
- 使用中文回复
- 结构清晰，分为以上5个部分
- 包含具体数值和价格水平
- 给出可操作的交易建议
"""
    
    with open(filename, "w", encoding="utf-8") as f:
        f.write(content)
    
    print(f"提问指南已生成: {filename}")
    return filename