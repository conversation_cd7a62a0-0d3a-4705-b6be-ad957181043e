# live_monitor.py
"""
实时监控BTCUSDT价格和技术指标
"""
import time
import datetime
from data_fetcher import get_latest_price, fetch_realtime_klines
from technical_analysis import calculate_indicators
from config import SYMBOL, INTERVAL

def live_monitor(update_interval=60):
    """
    实时监控价格和技术指标
    
    Args:
        update_interval: 更新间隔（秒），默认60秒
    """
    print(f"开始实时监控 {SYMBOL} {INTERVAL}")
    print(f"更新间隔: {update_interval}秒")
    print("按 Ctrl+C 停止监控\n")
    
    try:
        while True:
            try:
                # 获取当前时间
                current_time = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                
                # 获取最新价格
                current_price = get_latest_price()
                
                # 获取K线数据并计算指标
                df = fetch_realtime_klines()
                df = calculate_indicators(df)
                
                # 获取最新指标
                latest = df.iloc[-1]
                
                # 清屏并显示信息
                print("\033[2J\033[H")  # 清屏
                print("=" * 60)
                print(f"🔴 {SYMBOL} 实时监控 | {current_time}")
                print("=" * 60)
                print(f"💰 当前价格: ${current_price:,.2f}")
                print(f"📊 最新K线时间: {df.index[-1]}")
                print()
                
                # 技术指标
                print("📈 技术指标:")
                print(f"  RSI(14):     {latest['RSI']:.2f}")
                print(f"  MACD:        {latest['MACD']:.4f}")
                print(f"  MACD信号:    {latest['MACD_signal']:.4f}")
                print(f"  ADX:         {latest['ADX']:.2f}")
                print(f"  ATR:         {latest['ATR']:.2f}")
                print()
                
                # 支撑阻力位
                print("🎯 关键位置:")
                print(f"  支撑位:      ${latest['key_support']:,.2f}")
                print(f"  阻力位:      ${latest['key_resistance']:,.2f}")
                print(f"  交易建议:    {latest['trade_suggestion']}")
                print()
                
                # 移动平均线
                print("📊 移动平均线:")
                if 'SMA_20' in latest:
                    print(f"  SMA(20):     ${latest['SMA_20']:,.2f}")
                if 'SMA_50' in latest:
                    print(f"  SMA(50):     ${latest['SMA_50']:,.2f}")
                if 'EMA_12' in latest:
                    print(f"  EMA(12):     ${latest['EMA_12']:,.2f}")
                if 'EMA_26' in latest:
                    print(f"  EMA(26):     ${latest['EMA_26']:,.2f}")
                print()
                
                # 价格变化
                price_change = latest['pct_change']
                change_symbol = "📈" if price_change > 0 else "📉" if price_change < 0 else "➡️"
                print(f"{change_symbol} 价格变化: {price_change:+.2f}%")
                print()
                
                # RSI信号
                rsi = latest['RSI']
                if rsi > 70:
                    print("⚠️  RSI超买信号 (>70)")
                elif rsi < 30:
                    print("⚠️  RSI超卖信号 (<30)")
                else:
                    print("✅ RSI正常范围")
                
                print(f"\n下次更新: {update_interval}秒后...")
                
            except Exception as e:
                print(f"❌ 获取数据失败: {e}")
                print(f"将在{update_interval}秒后重试...")
            
            # 等待下次更新
            time.sleep(update_interval)
            
    except KeyboardInterrupt:
        print("\n\n👋 监控已停止")

def quick_check():
    """快速检查当前状态"""
    try:
        current_price = get_latest_price()
        df = fetch_realtime_klines()
        df = calculate_indicators(df)
        latest = df.iloc[-1]
        
        print(f"💰 {SYMBOL} 当前价格: ${current_price:,.2f}")
        print(f"📊 RSI: {latest['RSI']:.2f}")
        print(f"🎯 支撑位: ${latest['key_support']:,.2f} | 阻力位: ${latest['key_resistance']:,.2f}")
        print(f"💡 建议: {latest['trade_suggestion']}")
        
    except Exception as e:
        print(f"❌ 获取数据失败: {e}")

if __name__ == "__main__":
    import sys
    
    if len(sys.argv) > 1:
        if sys.argv[1] == "quick":
            quick_check()
        elif sys.argv[1] == "monitor":
            interval = int(sys.argv[2]) if len(sys.argv) > 2 else 60
            live_monitor(interval)
        else:
            print("用法:")
            print("  python live_monitor.py quick     # 快速检查")
            print("  python live_monitor.py monitor [间隔秒数]  # 实时监控")
    else:
        live_monitor()
