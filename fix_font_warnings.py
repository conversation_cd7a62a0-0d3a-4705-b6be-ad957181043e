# fix_font_warnings.py
"""
修复matplotlib中文字体警告的快速脚本
"""
import matplotlib.pyplot as plt
import matplotlib.font_manager as fm
import warnings

def suppress_font_warnings():
    """抑制字体警告"""
    warnings.filterwarnings('ignore', category=UserWarning, module='matplotlib')
    print("✅ Font warnings suppressed")

def list_available_fonts():
    """列出系统可用字体"""
    print("Available fonts on system:")
    fonts = sorted([f.name for f in fm.fontManager.ttflist])
    
    # 查找中文字体
    chinese_fonts = [f for f in fonts if any(keyword in f.lower() for keyword in 
                    ['simhei', 'simsun', 'microsoft', 'yahei', 'kaiti', 'fangsong'])]
    
    print(f"Total fonts: {len(fonts)}")
    print(f"Chinese fonts found: {len(chinese_fonts)}")
    
    if chinese_fonts:
        print("Chinese fonts available:")
        for font in chinese_fonts[:5]:  # 只显示前5个
            print(f"  - {font}")
    else:
        print("No Chinese fonts found - using English only is recommended")
    
    return fonts, chinese_fonts

def configure_safe_fonts():
    """配置安全的字体设置"""
    # 使用最安全的字体配置
    plt.rcParams['font.family'] = ['DejaVu Sans']
    plt.rcParams['font.sans-serif'] = ['DejaVu Sans']
    plt.rcParams['axes.unicode_minus'] = False
    plt.rcParams['font.size'] = 10
    
    print("✅ Safe font configuration applied")

def test_chart_without_chinese():
    """测试不含中文的图表"""
    import numpy as np
    
    # 创建测试数据
    x = np.linspace(0, 10, 100)
    y = np.sin(x)
    
    plt.figure(figsize=(10, 6))
    plt.plot(x, y, label='Sine Wave', linewidth=2)
    plt.title('Test Chart - English Only', fontsize=14)
    plt.xlabel('X Values', fontsize=12)
    plt.ylabel('Y Values', fontsize=12)
    plt.legend()
    plt.grid(True, alpha=0.3)
    
    # 添加文字标注
    plt.text(5, 0.5, 'Test Annotation', fontsize=12, 
             bbox=dict(boxstyle="round", facecolor="yellow", alpha=0.7))
    
    plt.savefig('test_english_chart.png', dpi=150, bbox_inches='tight')
    plt.close()
    
    print("✅ Test chart created: test_english_chart.png")

def create_font_config_file():
    """创建字体配置文件"""
    config_content = """
# matplotlib font configuration
# Place this in your script before importing matplotlib.pyplot

import matplotlib
matplotlib.rcParams['font.family'] = ['DejaVu Sans', 'Arial', 'sans-serif']
matplotlib.rcParams['font.sans-serif'] = ['DejaVu Sans', 'Arial']
matplotlib.rcParams['axes.unicode_minus'] = False

# Suppress font warnings
import warnings
warnings.filterwarnings('ignore', category=UserWarning, module='matplotlib')
"""
    
    with open('font_config.py', 'w', encoding='utf-8') as f:
        f.write(config_content)
    
    print("✅ Font configuration file created: font_config.py")

def fix_visualization_file():
    """修复visualization.py文件中的中文问题"""
    print("Checking visualization.py for Chinese characters...")
    
    try:
        with open('visualization.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查是否包含中文字符
        chinese_chars = []
        for i, char in enumerate(content):
            if '\u4e00' <= char <= '\u9fff':  # 中文Unicode范围
                chinese_chars.append((i, char))
        
        if chinese_chars:
            print(f"Found {len(chinese_chars)} Chinese characters in visualization.py")
            print("First few Chinese characters:")
            for i, (pos, char) in enumerate(chinese_chars[:10]):
                print(f"  Position {pos}: '{char}'")
            
            print("\n💡 Recommendation:")
            print("1. Replace all Chinese text with English")
            print("2. Use the create_english_chart.py script instead")
            print("3. Or suppress warnings with warnings.filterwarnings()")
        else:
            print("✅ No Chinese characters found in visualization.py")
            
    except FileNotFoundError:
        print("❌ visualization.py not found")
    except Exception as e:
        print(f"❌ Error reading file: {e}")

def main():
    """主修复函数"""
    print("=" * 60)
    print("Matplotlib Font Warning Fix Tool")
    print("=" * 60)
    
    # 1. 抑制警告
    suppress_font_warnings()
    
    # 2. 检查可用字体
    fonts, chinese_fonts = list_available_fonts()
    
    # 3. 配置安全字体
    configure_safe_fonts()
    
    # 4. 测试图表
    test_chart_without_chinese()
    
    # 5. 创建配置文件
    create_font_config_file()
    
    # 6. 检查现有文件
    fix_visualization_file()
    
    print("\n" + "=" * 60)
    print("Fix Summary:")
    print("=" * 60)
    print("✅ Font warnings suppressed")
    print("✅ Safe font configuration applied")
    print("✅ Test chart created without warnings")
    print("✅ Configuration file created")
    
    print("\n💡 Solutions for your project:")
    print("1. Add this to the top of visualization.py:")
    print("   import warnings")
    print("   warnings.filterwarnings('ignore', category=UserWarning, module='matplotlib')")
    print()
    print("2. Or use the create_english_chart.py script")
    print()
    print("3. Or replace all Chinese text with English in your charts")
    
    print("=" * 60)

if __name__ == "__main__":
    main()
