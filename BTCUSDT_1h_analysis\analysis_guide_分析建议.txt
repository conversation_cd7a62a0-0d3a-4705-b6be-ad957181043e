
请分析提供的交易数据文件，完成以下任务：

1. 支撑位/阻力位分析
   - 识别当前价格附近的关键支撑位和阻力位
   - 评估每个支撑位/阻力位的强度
   - 预测这些水平位的可靠性

2. 趋势分析
   - 确定当前市场趋势方向（上涨/下跌/盘整）
   - 评估趋势强度（基于ADX和价格行为）
   - 识别潜在的反转信号

3. 交易策略建议
   - 提供明确的做多/做空建议
   - 建议具体的入场点位
   - 设置止损位和目标位
   - 计算风险/回报比

4. 风险管理
   - 基于ATR建议止损距离
   - 建议仓位大小
   - 识别潜在风险事件

5. 图表解读
   - 描述图表中的关键形态
   - 分析异常交易量区域
   - 标记图表中的关键水平位

数据说明：
- 主数据文件: BTCUSDT_1h_7days_data.csv
- 包含指标: 开盘价, 最高价, 最低价, 收盘价, 成交量, RSI, MACD, 布林带, ATR, 支撑位/阻力位等
- 时间范围: 最近7天
- K线周期: 1h

输出要求:
- 使用中文回复
- 结构清晰，分为以上5个部分
- 包含具体数值和价格水平
- 给出可操作的交易建议
