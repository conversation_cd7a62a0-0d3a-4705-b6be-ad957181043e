# test_run.py
"""
简单测试脚本，验证主程序功能
"""
import os
import sys

def check_files():
    """检查必要文件是否存在"""
    required_files = [
        'main.py',
        'config.py', 
        'data_fetcher.py',
        'technical_analysis.py',
        'report_generator.py'
    ]
    
    missing_files = []
    for file in required_files:
        if not os.path.exists(file):
            missing_files.append(file)
    
    if missing_files:
        print(f"❌ 缺少文件: {missing_files}")
        return False
    else:
        print("✅ 所有必要文件都存在")
        return True

def test_imports():
    """测试模块导入"""
    try:
        from config import SYMBOL, INTERVAL, DAYS_TO_ANALYZE
        print(f"✅ 配置导入成功: {SYMBOL} {INTERVAL} {DAYS_TO_ANALYZE}天")
        
        from data_fetcher import fetch_klines
        print("✅ 数据获取模块导入成功")
        
        from technical_analysis import calculate_indicators
        print("✅ 技术分析模块导入成功")
        
        from report_generator import generate_full_report, save_optimized_csv
        print("✅ 报告生成模块导入成功")
        
        return True
        
    except ImportError as e:
        print(f"❌ 导入失败: {e}")
        return False

def run_main():
    """运行主程序"""
    print("\n🚀 运行主程序...")
    try:
        from main import main
        main()
        return True
    except Exception as e:
        print(f"❌ 主程序运行失败: {e}")
        return False

def main():
    """测试主函数"""
    print("=" * 50)
    print("程序功能测试")
    print("=" * 50)
    
    # 1. 检查文件
    if not check_files():
        return
    
    # 2. 测试导入
    if not test_imports():
        return
    
    # 3. 运行主程序
    if run_main():
        print("\n✅ 测试完成！程序运行正常")
    else:
        print("\n❌ 测试失败！请检查错误信息")

if __name__ == "__main__":
    main()
