# fix_file_permission.py
"""
修复文件权限问题的辅助脚本
"""
import os
import glob
import time

def check_file_permissions():
    """检查CSV文件的权限状态"""
    csv_files = glob.glob("BTCUSDT_*.csv")
    
    if not csv_files:
        print("✅ 没有找到CSV文件")
        return True
    
    print("检查CSV文件状态:")
    all_ok = True
    
    for file in csv_files:
        try:
            # 尝试以写入模式打开文件
            with open(file, 'a') as f:
                pass
            print(f"✅ {file} - 可写入")
        except PermissionError:
            print(f"❌ {file} - 被占用或权限不足")
            all_ok = False
        except Exception as e:
            print(f"⚠️  {file} - 其他错误: {e}")
            all_ok = False
    
    return all_ok

def backup_old_files():
    """备份旧的CSV文件"""
    csv_files = glob.glob("BTCUSDT_*.csv")
    
    if not csv_files:
        print("没有需要备份的文件")
        return
    
    backup_dir = "backup"
    if not os.path.exists(backup_dir):
        os.makedirs(backup_dir)
        print(f"创建备份目录: {backup_dir}")
    
    for file in csv_files:
        try:
            backup_name = os.path.join(backup_dir, f"backup_{int(time.time())}_{file}")
            os.rename(file, backup_name)
            print(f"✅ 备份: {file} -> {backup_name}")
        except Exception as e:
            print(f"❌ 备份失败 {file}: {e}")

def clean_old_files():
    """清理旧的分析文件"""
    patterns = [
        "BTCUSDT_*_analysis.png",
        "BTCUSDT_*_trend_volatility.png", 
        "BTCUSDT_*.csv"
    ]
    
    print("清理旧文件:")
    for pattern in patterns:
        files = glob.glob(pattern)
        for file in files:
            try:
                os.remove(file)
                print(f"✅ 删除: {file}")
            except PermissionError:
                print(f"❌ 无法删除 {file} - 文件被占用")
            except Exception as e:
                print(f"❌ 删除失败 {file}: {e}")

def main():
    print("=== 文件权限修复工具 ===\n")
    
    print("1. 检查文件权限状态...")
    if check_file_permissions():
        print("✅ 所有文件权限正常\n")
    else:
        print("❌ 发现文件权限问题\n")
        
        choice = input("选择操作:\n1. 备份旧文件\n2. 清理所有旧文件\n3. 退出\n请输入选择 (1/2/3): ")
        
        if choice == "1":
            backup_old_files()
        elif choice == "2":
            confirm = input("⚠️  确定要删除所有旧文件吗? (y/N): ")
            if confirm.lower() == 'y':
                clean_old_files()
            else:
                print("操作已取消")
        else:
            print("退出")
    
    print("\n提示:")
    print("- 如果文件被Excel等程序打开，请先关闭这些程序")
    print("- 确保有足够的磁盘空间")
    print("- 检查防病毒软件是否阻止文件写入")

if __name__ == "__main__":
    main()
