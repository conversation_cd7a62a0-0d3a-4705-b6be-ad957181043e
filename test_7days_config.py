# test_7days_config.py
"""
测试7天配置是否正确
"""

def test_config():
    """测试配置参数"""
    print("🔍 测试7天配置...")
    
    try:
        from config import DAYS_TO_ANALYZE, LOOKBACK_PERIOD, ANALYSIS_WINDOW, PLOT_LAST_POINTS
        
        print(f"分析天数: {DAYS_TO_ANALYZE} 天")
        print(f"K线数量: {LOOKBACK_PERIOD} 根 ({LOOKBACK_PERIOD/24:.1f}天)")
        print(f"分析窗口: {ANALYSIS_WINDOW}")
        print(f"图表显示: {PLOT_LAST_POINTS} 根K线")
        
        # 验证计算是否正确
        expected_klines = DAYS_TO_ANALYZE * 24
        if LOOKBACK_PERIOD == expected_klines:
            print("✅ K线数量计算正确")
        else:
            print(f"❌ K线数量计算错误，应该是 {expected_klines}")
        
        if PLOT_LAST_POINTS == expected_klines:
            print("✅ 图表显示数量正确")
        else:
            print(f"❌ 图表显示数量错误，应该是 {expected_klines}")
        
        return True
        
    except ImportError as e:
        print(f"❌ 配置导入失败: {e}")
        return False

def test_data_fetch():
    """测试数据获取"""
    print(f"\n🌐 测试数据获取...")
    
    try:
        from data_fetcher import fetch_klines
        
        df = fetch_klines()
        if df is not None:
            print(f"✅ 成功获取 {len(df)} 条数据")
            print(f"时间范围: {df.index[0]} 到 {df.index[-1]}")
            
            # 计算实际天数
            time_diff = df.index[-1] - df.index[0]
            actual_days = time_diff.total_seconds() / (24 * 3600)
            print(f"实际时间跨度: {actual_days:.1f} 天")
            
            if actual_days >= 6.5:  # 允许一些误差
                print("✅ 数据时间跨度符合7天要求")
            else:
                print(f"⚠️ 数据时间跨度可能不足7天")
            
            return True
        else:
            print("❌ 数据获取失败")
            return False
            
    except Exception as e:
        print(f"❌ 数据获取测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🧪 7天配置测试")
    print("=" * 50)
    
    config_ok = test_config()
    data_ok = test_data_fetch()
    
    print(f"\n" + "=" * 50)
    print("测试结果:")
    print("=" * 50)
    
    if config_ok and data_ok:
        print("✅ 7天配置测试通过！")
        print("✅ 现在可以获取7天的1小时数据")
        print("✅ 运行 python main.py 开始分析")
    else:
        print("❌ 部分测试失败")
        if not config_ok:
            print("❌ 配置参数有问题")
        if not data_ok:
            print("❌ 数据获取有问题")
    
    print(f"\n📊 预期输出文件名将包含:")
    print(f"• BTCUSDT_1h_7days_data.csv")
    print(f"• BTCUSDT_1h_analysis_summary.md")
    print(f"• 4个PNG图表文件")

if __name__ == "__main__":
    main()
