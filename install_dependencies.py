# install_dependencies.py
"""
检查和安装项目依赖
"""
import subprocess
import sys
import importlib

def check_package(package_name, import_name=None):
    """检查包是否已安装"""
    if import_name is None:
        import_name = package_name
    
    try:
        importlib.import_module(import_name)
        return True
    except ImportError:
        return False

def install_package(package_name):
    """安装包"""
    print(f"正在安装 {package_name}...")
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", package_name])
        print(f"✅ {package_name} 安装成功")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ {package_name} 安装失败: {e}")
        return False

def main():
    """主函数"""
    print("🔍 检查项目依赖...")
    print("=" * 50)
    
    # 定义所需的包
    required_packages = [
        ("pandas", "pandas"),
        ("numpy", "numpy"), 
        ("python-binance", "binance"),
        ("TA-Lib", "talib"),
        ("matplotlib", "matplotlib")
    ]
    
    missing_packages = []
    
    # 检查每个包
    for package_name, import_name in required_packages:
        if check_package(package_name, import_name):
            print(f"✅ {package_name} 已安装")
        else:
            print(f"❌ {package_name} 未安装")
            missing_packages.append(package_name)
    
    # 安装缺失的包
    if missing_packages:
        print(f"\n📦 需要安装 {len(missing_packages)} 个包:")
        for package in missing_packages:
            print(f"   • {package}")
        
        print(f"\n开始安装...")
        
        success_count = 0
        for package in missing_packages:
            if install_package(package):
                success_count += 1
        
        print(f"\n安装结果: {success_count}/{len(missing_packages)} 个包安装成功")
        
        if success_count == len(missing_packages):
            print("✅ 所有依赖安装完成！")
        else:
            print("⚠️ 部分依赖安装失败，请手动安装")
            
            print(f"\n手动安装命令:")
            for package in missing_packages:
                print(f"pip install {package}")
    else:
        print(f"\n✅ 所有依赖都已安装！")
    
    # 特别处理TA-Lib
    if not check_package("TA-Lib", "talib"):
        print(f"\n⚠️ TA-Lib 安装可能需要特殊处理:")
        print(f"Windows用户:")
        print(f"  1. 访问 https://www.lfd.uci.edu/~gohlke/pythonlibs/#ta-lib")
        print(f"  2. 下载对应Python版本的.whl文件")
        print(f"  3. 运行: pip install 下载的文件名.whl")
        print(f"")
        print(f"或者使用conda:")
        print(f"  conda install -c conda-forge ta-lib")
    
    print(f"\n🚀 安装完成后，运行以下命令测试:")
    print(f"python main.py")

if __name__ == "__main__":
    main()
