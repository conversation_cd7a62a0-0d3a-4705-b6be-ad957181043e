import pandas as pd
import talib
from binance.um_futures import UMFutures
from datetime import datetime, timedelta, timezone
import time

# 设置北京时区 (UTC+8)
BEIJING_TZ = timezone(timedelta(hours=8))


def fetch_btcusdt_data(days=5, interval='1h'):
    """
    获取Binance BTCUSDT永续合约K线数据并计算技术指标

    参数:
        days (int): 获取数据的天数范围
        interval (str): K线间隔 (如'1h', '4h', '1d')

    返回:
        pd.DataFrame: 包含原始数据和技术指标的数据框
    """
    try:
        print(f"开始获取BTCUSDT永续合约数据: {days}天, {interval}线")

        # 1. 初始化Binance客户端
        client = UMFutures()

        # 2. 计算时间范围 (使用UTC时间)
        utc_now = datetime.now(timezone.utc)
        utc_end = utc_now.replace(minute=0, second=0, microsecond=0)
        utc_start = utc_end - timedelta(days=days)

        print(f"数据范围 (UTC): {utc_start} 至 {utc_end}")

        # 3. 分页获取所有K线数据
        all_klines = []
        current_end = utc_end

        while True:
            # 获取当前页数据
            klines = client.klines(
                symbol="BTCUSDT",
                interval=interval,
                endTime=int(current_end.timestamp() * 1000),
                limit=1000
            )

            if not klines:
                break

            # 获取第一条K线的时间
            first_kline_time = datetime.fromtimestamp(klines[0][0] / 1000, tz=timezone.utc)

            # 检查是否超出所需时间范围
            if first_kline_time < utc_start:
                # 只保留在时间范围内的数据
                klines = [k for k in klines if datetime.fromtimestamp(k[0] / 1000, tz=timezone.utc) >= utc_start]
                all_klines.extend(klines)
                break

            all_klines.extend(klines)

            # 更新结束时间为上一批数据的最早时间
            current_end = datetime.fromtimestamp(klines[-1][0] / 1000, tz=timezone.utc) - timedelta(seconds=1)

            # 避免API限流
            time.sleep(0.1)

            # 检查是否达到起始时间
            if current_end <= utc_start:
                break

        if not all_klines:
            print("未获取到任何数据")
            return None

        print(f"成功获取 {len(all_klines)} 条K线数据")

        # 4. 创建DataFrame
        columns = [
            'open_time', 'open', 'high', 'low', 'close', 'volume',
            'close_time', 'quote_asset_volume', 'number_of_trades',
            'taker_buy_base_volume', 'taker_buy_quote_volume', 'ignore'
        ]

        df = pd.DataFrame(all_klines, columns=columns)

        # 转换时间列
        df['open_time_utc'] = pd.to_datetime(df['open_time'], unit='ms').dt.tz_localize('UTC')
        df['open_time_beijing'] = df['open_time_utc'].dt.tz_convert(BEIJING_TZ)

        # 转换数值列
        numeric_cols = ['open', 'high', 'low', 'close', 'volume']
        df[numeric_cols] = df[numeric_cols].apply(pd.to_numeric)

        # 5. 计算技术指标 (需要按时间顺序排列)
        df = df.sort_values('open_time_utc')

        # 提取价格数组
        closes = df['close'].values.astype(float)
        highs = df['high'].values.astype(float)
        lows = df['low'].values.astype(float)

        # 计算指标
        df['RSI_14'] = talib.RSI(closes, timeperiod=14)  # 14周期RSI
        df['MACD'], df['MACD_signal'], df['MACD_hist'] = talib.MACD(closes)  # 默认12,26,9
        df['SMA_20'] = talib.SMA(closes, timeperiod=20)  # 20周期简单移动平均
        df['EMA_20'] = talib.EMA(closes, timeperiod=20)  # 20周期指数移动平均
        df['BB_upper'], df['BB_middle'], df['BB_lower'] = talib.BBANDS(closes, timeperiod=20)  # 布林带

        # 6. 排序为最新数据在前
        df = df.sort_values('open_time_utc', ascending=False)

        # 7. 保存数据
        timestamp = datetime.now(BEIJING_TZ).strftime("%Y%m%d_%H%M%S")
        filename = f"BTCUSDT_{days}days_{interval}_{timestamp}.csv"

        # 选择要保存的列
        output_cols = [
            'open_time_beijing', 'open', 'high', 'low', 'close', 'volume',
            'RSI_14', 'MACD', 'MACD_signal', 'MACD_hist', 'SMA_20', 'EMA_20',
            'BB_upper', 'BB_middle', 'BB_lower'
        ]

        # 保存为CSV
        df[output_cols].to_csv(filename, index=False, encoding='utf-8')
        print(f"数据已保存至: {filename}")

        # 显示最新数据
        print("\n最新数据预览:")
        print(df[output_cols].head(3))

        return df

    except Exception as e:
        print(f"发生错误: {type(e).__name__} - {str(e)}")
        import traceback
        traceback.print_exc()
        return None


if __name__ == "__main__":
    # 获取最近3天1小时线数据
    data = fetch_btcusdt_data(days=3, interval='1h')

    # 获取最近7天4小时线数据
    # data = fetch_btcusdt_data(days=7, interval='4h')

    # 获取最近30天日线数据
    # data = fetch_btcusdt_data(days=30, interval='1d')