# test_no_chinese.py
"""
测试是否还有中文字符导致字体警告
"""
import pandas as pd
import numpy as np
from datetime import datetime, timedelta

def create_test_data():
    """创建测试数据"""
    dates = pd.date_range(start=datetime.now() - timedelta(hours=120), 
                         end=datetime.now(), freq='H')
    
    np.random.seed(42)
    data = {
        'open': 60000 + np.random.randn(len(dates)) * 100,
        'high': 60000 + np.random.randn(len(dates)) * 100 + 50,
        'low': 60000 + np.random.randn(len(dates)) * 100 - 50,
        'close': 60000 + np.random.randn(len(dates)) * 100,
        'volume': np.random.randint(100, 1000, len(dates)),
        'pct_change': np.random.randn(len(dates)) * 2,
        'TR': np.random.rand(len(dates)) * 100
    }
    
    df = pd.DataFrame(data, index=dates)
    return df

def test_technical_analysis():
    """测试技术分析模块"""
    print("测试技术分析模块...")
    
    try:
        from technical_analysis import calculate_indicators
        
        # 创建测试数据
        df = create_test_data()
        
        # 计算技术指标
        df_with_indicators = calculate_indicators(df)
        
        # 检查交易建议是否为英文
        trade_suggestions = df_with_indicators['trade_suggestion'].unique()
        print(f"交易建议值: {trade_suggestions}")
        
        # 检查是否包含中文
        has_chinese = any('\u4e00' <= char <= '\u9fff' for suggestion in trade_suggestions for char in str(suggestion))
        
        if has_chinese:
            print("❌ 交易建议中仍包含中文字符")
            return False
        else:
            print("✅ 交易建议已全部使用英文")
            return True
            
    except Exception as e:
        print(f"❌ 技术分析测试失败: {e}")
        return False

def test_visualization():
    """测试可视化模块"""
    print("测试可视化模块...")
    
    try:
        from technical_analysis import calculate_indicators
        from visualization import plot_technical_analysis
        
        # 创建测试数据
        df = create_test_data()
        df = calculate_indicators(df)
        
        # 生成图表
        chart_file = plot_technical_analysis(df)
        
        print(f"✅ 图表生成成功: {chart_file}")
        return True
        
    except Exception as e:
        print(f"❌ 可视化测试失败: {e}")
        return False

def check_chinese_in_files():
    """检查文件中的中文字符"""
    files_to_check = ['technical_analysis.py', 'report_generator.py', 'visualization.py']
    
    print("检查文件中的中文字符...")
    
    for filename in files_to_check:
        try:
            with open(filename, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 查找中文字符（排除注释）
            lines = content.split('\n')
            chinese_in_code = []
            
            for i, line in enumerate(lines, 1):
                # 跳过注释行
                if line.strip().startswith('#') or '"""' in line or "'''" in line:
                    continue
                
                # 检查代码行中的中文
                for char in line:
                    if '\u4e00' <= char <= '\u9fff':
                        chinese_in_code.append((i, line.strip()))
                        break
            
            if chinese_in_code:
                print(f"❌ {filename} 中发现中文字符:")
                for line_num, line_content in chinese_in_code[:5]:  # 只显示前5个
                    print(f"   行 {line_num}: {line_content}")
            else:
                print(f"✅ {filename} 代码中无中文字符")
                
        except FileNotFoundError:
            print(f"⚠️  文件不存在: {filename}")
        except Exception as e:
            print(f"❌ 检查 {filename} 时出错: {e}")

def main():
    """主测试函数"""
    print("=" * 60)
    print("中文字符检查和字体警告修复测试")
    print("=" * 60)
    
    # 1. 检查文件中的中文字符
    check_chinese_in_files()
    
    print("\n" + "-" * 40)
    
    # 2. 测试技术分析
    tech_ok = test_technical_analysis()
    
    print("\n" + "-" * 40)
    
    # 3. 测试可视化
    viz_ok = test_visualization()
    
    print("\n" + "=" * 60)
    print("测试总结:")
    print("=" * 60)
    
    if tech_ok and viz_ok:
        print("✅ 所有测试通过！")
        print("✅ 中文字符问题已修复")
        print("✅ 现在运行 main.py 应该不会出现字体警告")
    else:
        print("❌ 部分测试失败，请检查相关问题")
    
    print("\n💡 修复内容:")
    print("- 交易建议改为英文 (STRONG BUY, SELL, HOLD 等)")
    print("- 移除了所有可能传递到图表的中文字符")
    print("- 保留了注释中的中文（不影响显示）")

if __name__ == "__main__":
    main()
